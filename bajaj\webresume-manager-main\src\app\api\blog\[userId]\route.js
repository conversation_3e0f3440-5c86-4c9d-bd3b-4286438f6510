// app/api/blog/[userId]

import { auth } from "@/lib/auth";
import { connectToDB } from "@/lib/mongodb";
import Blog from "@/models/blog";
import User from "@/models/user";
import mongoose from "mongoose";
import { NextResponse } from "next/server";
import { z } from "zod";

export async function POST(req) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized", success: false });
    }

    await connectToDB();

    const user = await User.findOne({
      email: session.user.email,
    });

    if (!user) {
      return NextResponse.json({ error: "User not found", success: false });
    }

    const body = await req.json();

    const schema = z.object({
      title: z.string().min(1),
      desc: z.string().min(10),
      category: z.string().min(1),
      image: z.string().optional(),
    });

    const parsed = schema.safeParse(body);
    if (!parsed.success) {
      return NextResponse.json({
        error: parsed.error.flatten().fieldErrors,
        success: false,
      });
    }

    const blog = new Blog({
      title: parsed.data.title,
      category: parsed.data.category,
      desc: parsed.data.desc,
      image: parsed.data.image || "",
      user: user._id,
    });

    await blog.save();

    return NextResponse.json(blog, { status: 201 });
  } catch (error) {
    console.error("POST error:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

export async function GET(req, { params }) {
  try {
    await connectToDB();
    const { userId } = await params;

    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return NextResponse.json({ error: "Invalid user ID", success: false });
    }

    const posts = await Blog.find({ user: userId }).sort({ createdAt: -1 });
    //console.log(posts);
    return NextResponse.json(posts, { status: 200 });
  } catch (error) {
    console.error("GET blog error:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
