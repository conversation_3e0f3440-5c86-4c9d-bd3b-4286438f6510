<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Bajaj Motors</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="css/styles.css" />
</head>

<body class="bg-gray-50">
   <!-- Header -->
  <header class="header-overlay fixed top-0 left-0 right-0 z-50">
    <!-- Top Bar -->
    <div class="bg-transparent py-2 top-bar">
      <div class="top-bar-content">
        <div class="top-bar-left">
          <img src="assets/golcha-logo.png" alt="golcha_logo" class="top-bar-logo" />
          <span class="top-bar-text">GOLCHHA GROUP WITH LEGACY OF 100 YEAR</span>
        </div>
        <div class="top-bar-right">
          <svg class="top-bar-icon" viewBox="0 0 23 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M6.89761 15.1618C8.28247 16.3099 10.0607 17 12.0001 17C16.4184 17 20.0001 13.4183 20.0001 9C20.0001 8.43095 19.9407 7.87578 19.8278 7.34036M6.89761 15.1618C5.12756 13.6944 4.00014 11.4789 4.00014 9C4.00014 4.58172 7.58186 1 12.0001 1C15.8494 1 19.0637 3.71853 19.8278 7.34036M6.89761 15.1618C8.85314 14.7147 11.1796 13.7828 13.526 12.4281C16.2564 10.8517 18.4773 9.01248 19.8278 7.34036M6.89761 15.1618C4.46844 15.7171 2.61159 15.5243 1.99965 14.4644C1.36934 13.3726 2.19631 11.5969 3.99999 9.70898M19.8278 7.34036C21.0796 5.79041 21.5836 4.38405 21.0522 3.46374C20.5134 2.53051 19.0095 2.26939 16.9997 2.59929"
              stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          </svg>
          <span class="top-bar-text">International website</span>
        </div>
      </div>
    </div>

    <!-- Main Navigation -->
    <div class="bg-transparent py-2 font-roboto lg:px-[150px] px-4">
      <nav class="floating-navbar bg-white my-4 px-6">
        <!-- Mobile Navigation -->
        <div class="lg:hidden flex items-center justify-between py-4">
          <!-- Mobile Menu Button -->
          <button class="flex items-center justify-center w-8 h-8" id="mobile-menu-btn">
            <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
          </button>

          <!-- Mobile Logo -->
          <img class="h-12" src="assets/logo.png" alt="logo" />

          <!-- Mobile BIKES Button -->
          <button class="text-sm font-medium text-gray-700 flex items-center space-x-1" id="mobile-bikes-btn">
            <span>BIKES</span>
            <svg class="w-4 h-4 transition-transform duration-200" id="mobile-bikes-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
        </div>

        <!-- Desktop Navigation -->
        <div class="hidden lg:flex justify-evenly items-center py-4 text-base">
          <!-- Left Navigation Items -->
          <div class="flex items-center space-x-8">
            <!-- Motorcycles Dropdown -->
            <div class="relative dropdown">
              <button
                class="text-sm flex items-center space-x-1 text-gray-700 hover:text-blue-600 transition-colors duration-200"
                onclick="toggleDropdown('motorcycles')">
                <span>MOTORCYCLES</span>
                <svg class="w-4 h-4 transition-transform duration-200" id="motorcycles-arrow" fill="none"
                  stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </button>

              <!-- Dropdown Content -->
              <div id="motorcycles-dropdown"
                class="dropdown-content absolute top-full left-0 mt-12 bg-white border border-gray-200 z-50 overflow-hidden hidden"
                style="width: 1000px; height: 600px;">
                <div class="flex h-full">
                  <!-- Left Sidebar - Brands -->
                  <div class="w-64 bg-gray-50 p-6 rounded-l-lg flex-shrink-0">
                    <h3 class="text-gray-800 font-semibold mb-4">BRANDS</h3>
                    <ul class="space-y-2" id="brand-list">
                      <li><a href="#" class="brand-item active block px-3 py-2 text-sm text-gray-700 rounded-md transition-colors duration-200" data-brand="PULSAR">PULSAR</a></li>
                      <li><a href="#" class="brand-item block px-3 py-2 text-sm text-gray-700 rounded-md transition-colors duration-200" data-brand="DOMINAR">DOMINAR</a></li>
                      <li><a href="#" class="brand-item block px-3 py-2 text-sm text-gray-700 rounded-md transition-colors duration-200" data-brand="AVENGERS">AVENGERS</a></li>
                      <li><a href="#" class="brand-item block px-3 py-2 text-sm text-gray-700 rounded-md transition-colors duration-200" data-brand="DISCOVER">DISCOVER</a></li>
                      <li><a href="#" class="brand-item block px-3 py-2 text-sm text-gray-700 rounded-md transition-colors duration-200" data-brand="PLATINA">PLATINA</a></li>
                    </ul>
                  </div>

                  <!-- Right Content - Motorcycles -->
                  <div class="flex-1 flex flex-col h-full">
                    <!-- Category Filter -->
                    <div class="flex space-x-4 p-6 pb-4 flex-shrink-0 border-b border-gray-100">
                      <button
                        class="category-btn active px-4 py-2 rounded-full text-sm font-medium transition-all duration-200"
                        data-category="All">
                        All
                      </button>
                      <button
                        class="category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-200"
                        data-category="Classic">
                        Classic
                      </button>
                      <button
                        class="category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-200"
                        data-category="NS">
                        NS
                      </button>
                      <button
                        class="category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-200"
                        data-category="N">
                        N
                      </button>
                      <button
                        class="category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-200"
                        data-category="Adventure">
                        Adventure
                      </button>
                      <button
                        class="category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-200"
                        data-category="Cruiser">
                        Cruiser
                      </button>
                      <button
                        class="category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-200"
                        data-category="Commuter">
                        Commuter
                      </button>
                      <button
                        class="category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-200"
                        data-category="Economy">
                        Economy
                      </button>
                    </div>

                    <!-- Motorcycle Grid -->
                    <div id="motorcycle-grid" class="flex-1 overflow-y-auto px-6 py-4">
                      <!-- Pulsar Section -->
                      <div class="motorcycle-section active" data-brand="PULSAR">
                        <!-- Classic Category -->
                        <div class="category-section" data-category="Classic">
                          <div class="grid grid-cols-3 gap-6">
                            <div class="motorcycle-card">
                              <img src="./assets/bikes/pulsar/pulsar_220f_abs.png" alt="PULSAR 220F ABS" class="w-full h-32 object-contain">
                              <h4 class="text-sm font-medium mt-2">PULSAR 220F ABS</h4>
                            </div>
                            <div class="motorcycle-card">
                              <img src="./assets/bikes/pulsar/pulsar_150_td.png" alt="PULSAR 150 TD" class="w-full h-32 object-contain">
                              <h4 class="text-sm font-medium mt-2">PULSAR 150 TD</h4>
                            </div>
                            <!-- Add other Pulsar Classic models -->
                          </div>
                        </div>
                        <!-- Add other Pulsar categories -->
                      </div>

                      <!-- Dominar Section -->
                      <div class="motorcycle-section" data-brand="DOMINAR">
                        <!-- Adventure Category -->
                        <div class="category-section" data-category="Adventure">
                          <div class="grid grid-cols-3 gap-6">
                            <div class="motorcycle-card">
                              <img src="./assets/bikes/dominar/dominar_400.png" alt="DOMINAR 400" class="w-full h-32 object-contain">
                              <h4 class="text-sm font-medium mt-2">DOMINAR 400</h4>
                            </div>
                            <div class="motorcycle-card">
                              <img src="./assets/bikes/dominar/dominar_250.png" alt="DOMINAR 250" class="w-full h-32 object-contain">
                              <h4 class="text-sm font-medium mt-2">DOMINAR 250</h4>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Add other brand sections -->
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <a href="#" class="text-gray-700 text-sm hover:text-blue-600 transition-colors duration-200">SHOWROOMS</a>
            <a href="#" class="text-gray-700 text-sm hover:text-blue-600 transition-colors duration-200">WORKSHOPS</a>
            <a href="#" class="text-gray-700 text-sm hover:text-blue-600 transition-colors duration-200">EVENTS</a>
          </div>

          <!-- Center Logo -->
          <img class="h-[72px] px-4" src="assets/logo.png" alt="logo" />

          <!-- Right Navigation Items -->
          <div class="flex text-sm items-center space-x-8">
            <a href="#" class="text-gray-700 hover:text-blue-600 transition-colors duration-200">BOOK TEST RIDE</a>
            <a href="/about.html" class="text-gray-700 hover:text-blue-600 transition-colors duration-200">ABOUT US</a>
            <a href="#" class="text-gray-700 hover:text-blue-600 transition-colors duration-200">NEWS</a>

            <!-- Media Center Dropdown -->
            <div class="relative dropdown">
              <button
                class="flex items-center space-x-1 text-gray-700 hover:text-blue-600 transition-colors duration-200"
                onclick="toggleDropdown('media')">
                <span>MEDIA CENTER</span>
                <svg class="w-4 h-4 transition-transform duration-200" id="media-arrow" fill="none"
                  stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </button>

              <!-- Media Dropdown Content -->
              <div id="media-dropdown"
                class="media-dropdown-content absolute top-full right-0 mt-12 bg-white border border-gray-200 z-50 hidden"
                style="width: 220px">
                <div class="py-2">
                  <a href="/about.html"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">ABOUT
                    US</a>
                  <a href="#"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">ANNOUNCEMENTS</a>
                  <a href="#"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">EVENTS</a>
                  <a href="/blogs.html"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">BLOGS</a>
                  <a href="#"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">DOWNLOAD
                    CENTER</a>
                  <a href="#"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">CONTACT
                    US</a>
                  <a href="#"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">FAQS</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </nav>

      <!-- Mobile Bikes Full-Screen Menu -->
      <div id="mobile-bikes-dropdown" class="lg:hidden fixed inset-0 bg-white z-50 transform translate-x-full transition-transform duration-300">
        <!-- Mobile Bikes Content -->
        <div class="h-full flex flex-col">
          <!-- Header -->
          <div class="flex items-center justify-between p-4 border-b border-gray-200">
            <button class="back-btn">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
            </button>
            <h2 class="text-lg font-semibold">BIKES</h2>
            <button class="close-btn">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          <!-- Brands List -->
          <div id="mobile-bikes-brands" class="flex-1 overflow-y-auto">
            <div class="mobile-brand-item">
              <span class="text-lg font-medium text-gray-900">PULSAR</span>
              <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </div>
            <!-- Add other brand items -->
          </div>
        </div>
      </div>

      <!-- Mobile Menu Full-Screen -->
      <div id="mobile-menu" class="lg:hidden fixed inset-0 bg-white z-50 transform translate-x-full transition-transform duration-300">
        <!-- Mobile Menu Content -->
        <div class="h-full flex flex-col">
          <!-- Header -->
          <div class="flex items-center justify-between p-4 border-b border-gray-200">
            <img class="h-8" src="assets/logo.png" alt="logo" />
            <button class="close-btn">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          <!-- Menu Items -->
          <div class="flex-1 overflow-y-auto">
            <div class="mobile-menu-item">
              <span class="text-lg font-medium text-gray-900">MOTORCYCLES</span>
              <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </div>
            <a href="#" class="mobile-menu-item">
              <span class="text-lg font-medium text-gray-900">SHOWROOMS</span>
            </a>
            <a href="#" class="mobile-menu-item">
              <span class="text-lg font-medium text-gray-900">WORKSHOPS</span>
            </a>
            <a href="#" class="mobile-menu-item">
              <span class="text-lg font-medium text-gray-900">EVENTS</span>
            </a>
            <a href="#" class="mobile-menu-item">
              <span class="text-lg font-medium text-gray-900">BOOK TEST RIDE</span>
            </a>
            <a href="/about.html" class="mobile-menu-item">
              <span class="text-lg font-medium text-gray-900">ABOUT US</span>
            </a>
            <a href="#" class="mobile-menu-item">
              <span class="text-lg font-medium text-gray-900">NEWS</span>
            </a>
            <div class="mobile-menu-item">
              <span class="text-lg font-medium text-gray-900">MEDIA CENTER</span>
              <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- Hero Carousel Section -->
  <section class="hero-carousel relative">
    <div class="carousel-container">
      <!-- Slide 1 -->
      <div class="carousel-slide active" id="hero-slide-1">
        <img src="./assets/hero_image_1.png" alt="Hero Image 1" class="w-full h-full object-cover">
      </div>
      
      <!-- Slide 2 -->
      <div class="carousel-slide" id="hero-slide-2">
        <img src="https://images.unsplash.com/photo-1609630875171-b1321377ee65?w=1920&h=1080&fit=crop" alt="Hero Image 2" class="w-full h-full object-cover">
      </div>
      
      <!-- Slide 3 -->
      <div class="carousel-slide" id="hero-slide-3">
        <img src="https://images.unsplash.com/photo-1568772585407-9361f9bf3a87?w=1920&h=1080&fit=crop" alt="Hero Image 3" class="w-full h-full object-cover">
      </div>
      
      <!-- Slide 4 -->
      <div class="carousel-slide" id="hero-slide-4">
        <img src="https://images.unsplash.com/photo-1605531179818-de32686e5e2e?w=1920&h=1080&fit=crop" alt="Hero Image 4" class="w-full h-full object-cover">
      </div>
      
      <!-- Slide 5 -->
      <div class="carousel-slide" id="hero-slide-5">
        <img src="https://images.unsplash.com/photo-1571068316344-75bc76f77890?w=1920&h=1080&fit=crop" alt="Hero Image 5" class="w-full h-full object-cover">
      </div>

      <!-- Carousel Controls -->
      <button class="carousel-control prev" aria-label="Previous slide">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
      </button>
      <button class="carousel-control next" aria-label="Next slide">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
      </button>

      <!-- Carousel Indicators -->
      <div class="carousel-indicators">
        <button class="indicator active" data-slide="0" aria-label="Go to slide 1"></button>
        <button class="indicator" data-slide="1" aria-label="Go to slide 2"></button>
        <button class="indicator" data-slide="2" aria-label="Go to slide 3"></button>
        <button class="indicator" data-slide="3" aria-label="Go to slide 4"></button>
        <button class="indicator" data-slide="4" aria-label="Go to slide 5"></button>
      </div>
    </div>
  </section>

  <!-- Bike Carousel Section -->
  <section class="w-full bg-white relative overflow-hidden">
    <!-- Background Text -->
    <div class="absolute inset-0 flex items-center justify-center pointer-events-none">
      <h1 class="text-[20vw] lg:text-[25vw] font-black text-indigo-100 select-none">PULSAR</h1>
    </div>

    <!-- Brand Navigation Tabs - Top -->
    <div class="relative z-10 pt-8 pb-4">
      <nav class="flex justify-center space-x-8 lg:space-x-16">
        <button class="brand-tab active text-lg lg:text-xl font-semibold text-gray-800 border-b-2 border-black pb-2" 
                data-brand="PULSAR" 
                data-logo="./assets/brand-logos/pulsar-logo.png"
                data-category="Sports"
                data-category-icon="./assets/icons/sports.png">PULSAR</button>
        <button class="brand-tab text-lg lg:text-xl font-semibold text-gray-400 border-b-2 border-transparent pb-2 hover:text-gray-600" 
                data-brand="DOMINAR"
                data-logo="./assets/brand-logos/dominar-logo.svg"
                data-category="Touring"
                data-category-icon="./assets/icons/touring.png">DOMINAR</button>
        <button class="brand-tab text-lg lg:text-xl font-semibold text-gray-400 border-b-2 border-transparent pb-2 hover:text-gray-600" 
                data-brand="AVENGERS"
                data-logo="./assets/brand-logos/avengers-logo.svg"
                data-category="Adventure"
                data-category-icon="./assets/icons/adventure.png">AVENGERS</button>
        <button class="brand-tab text-lg lg:text-xl font-semibold text-gray-400 border-b-2 border-transparent pb-2 hover:text-gray-600" 
                data-brand="DISCOVER"
                data-logo="./assets/brand-logos/discover-logo.svg"
                data-category="Commuter"
                data-category-icon="./assets/icons/commuter.png">DISCOVER</button>
        <button class="brand-tab text-lg lg:text-xl font-semibold text-gray-400 border-b-2 border-transparent pb-2 hover:text-gray-600" 
                data-brand="PLATINA"
                data-logo="./assets/brand-logos/platina-logo.svg"
                data-category="Economy"
                data-category-icon="./assets/icons/economy.png">PLATINA</button>
      </nav>
    </div>

    <!-- Main Content Area -->
    <div class="relative z-10 max-w-7xl mx-auto px-4 py-8">
      <!-- Bike Title and Description -->
      <div class="text-center mb-8">
        <h2 class="text-3xl lg:text-5xl font-bold text-gray-900 mb-4">PULSAR 220F ABS</h2>
        <p class="text-gray-600 text-base lg:text-lg max-w-3xl mx-auto leading-relaxed">
          Experience the perfect blend of power and style with the Pulsar 220F ABS. This high-performance motorcycle delivers an exhilarating ride with advanced features and superior handling.
        </p>
      </div>

      <!-- Main Bike Display Grid -->
      <div class="grid grid-cols-12 gap-4 lg:gap-8 items-center min-h-[500px]">
        <!-- Left Side - Navigation Arrow -->
        <div class="col-span-2 flex justify-center items-center">
          <button class="prev-btn w-12 h-12 lg:w-14 lg:h-14 rounded-full border border-gray-300 flex items-center justify-center hover:border-gray-500 hover:bg-gray-50 transition-all duration-200">
            <svg class="w-6 h-6 lg:w-7 lg:h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
        </div>

        <!-- Center - Main Bike Image -->
        <div class="col-span-8 flex justify-center items-center">
          <div class="relative w-full max-w-4xl">
            <img src="./assets/bikes/pulsar/pulsar_220f_abs.png" alt="Pulsar 220F ABS" class="w-full h-auto max-h-[400px] lg:max-h-[500px] object-contain">
            <!-- Explore More Button -->
            <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2">
              <a href="bike-detail.html" class="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors duration-200 shadow-lg">
                Explore More
                <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </a>
            </div>
          </div>
        </div>

        <!-- Right Side - Navigation Arrow -->
        <div class="col-span-2 flex justify-center items-center">
          <button class="next-btn w-12 h-12 lg:w-14 lg:h-14 rounded-full border border-gray-300 flex items-center justify-center hover:border-gray-500 hover:bg-gray-50 transition-all duration-200">
            <svg class="w-6 h-6 lg:w-7 lg:h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Bottom Section -->
      <div class="mt-8">
        <!-- Brand Logo and Category -->
        <div class="flex items-center justify-center space-x-4 mb-6">
          <img src="./assets/brand-logos/pulsar-logo.png" alt="Pulsar Logo" class="h-8">
          <div class="flex items-center space-x-2">
            <img src="./assets/icons/sports.png" alt="Sports Category" class="h-6">
            <span class="text-gray-600">Sports</span>
          </div>
        </div>

        <!-- Color Selection -->
        <div class="flex justify-center space-x-4 mb-6">
          <button class="color-btn active w-8 h-8 rounded-full bg-black border-2 border-gray-300" data-color="black"></button>
          <button class="color-btn w-8 h-8 rounded-full bg-yellow-400 border-2 border-gray-300" data-color="yellow"></button>
          <button class="color-btn w-8 h-8 rounded-full bg-green-600 border-2 border-gray-300" data-color="green"></button>
        </div>

        <!-- Series Link -->
        <div class="text-center mb-6">
          <a href="bike-detail.html" class="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors duration-200">
            View Series page
            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </a>
        </div>

        <!-- Model Tabs -->
        <div class="flex flex-wrap justify-center gap-4">
          <!-- Pulsar Models -->
          <div class="model-group pulsar-models">
            <button class="variant-btn active px-4 py-2 text-sm font-medium text-gray-700 border-b-2 border-black" 
                    data-model="pulsar-220f-abs"
                    data-image="./assets/bikes/pulsar/pulsar_220f_abs.png"
                    data-name="PULSAR 220F ABS"
                    data-description="Experience the perfect blend of power and style with the Pulsar 220F ABS. This high-performance motorcycle delivers an exhilarating ride with advanced features and superior handling.">PULSAR 220F ABS</button>
            <button class="variant-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent" 
                    data-model="pulsar-150-td"
                    data-image="./assets/bikes/pulsar/pulsar_150_td.png"
                    data-name="PULSAR 150 TD"
                    data-description="The Pulsar 150 TD combines style with efficiency, offering a perfect balance of performance and fuel economy for everyday riding.">PULSAR 150 TD</button>
            <button class="variant-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent" 
                    data-model="pulsar-ns200"
                    data-image="./assets/bikes/pulsar/pulsar_ns200.png"
                    data-name="PULSAR NS200"
                    data-description="The Pulsar NS200 is a sporty naked street bike that offers an exciting riding experience with its powerful engine and agile handling.">PULSAR NS200</button>
            <button class="variant-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent" 
                    data-model="pulsar-rs200"
                    data-image="./assets/bikes/pulsar/pulsar_rs200.png"
                    data-name="PULSAR RS200"
                    data-description="The Pulsar RS200 is a fully-faired sports bike that combines aggressive styling with high performance for an exhilarating ride.">PULSAR RS200</button>
          </div>

          <!-- Dominar Models -->
          <div class="model-group dominar-models hidden">
            <button class="variant-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent" 
                    data-model="dominar-400"
                    data-image="./assets/bikes/dominar/dominar_400.png"
                    data-name="DOMINAR 400"
                    data-description="The Dominar 400 is a powerful touring motorcycle designed for long-distance comfort and performance.">DOMINAR 400</button>
            <button class="variant-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent" 
                    data-model="dominar-250"
                    data-image="./assets/bikes/dominar/dominar_250.png"
                    data-name="DOMINAR 250"
                    data-description="The Dominar 250 offers a perfect balance of power and efficiency for urban commuting and weekend getaways.">DOMINAR 250</button>
          </div>

          <!-- Avengers Models -->
          <div class="model-group avengers-models hidden">
            <button class="variant-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent" 
                    data-model="avenger-220"
                    data-image="./assets/bikes/avengers/avenger_220.png"
                    data-name="AVENGER 220"
                    data-description="The Avenger 220 combines cruiser comfort with sporty performance for an unmatched riding experience.">AVENGER 220</button>
            <button class="variant-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent" 
                    data-model="avenger-160"
                    data-image="./assets/bikes/avengers/avenger_160.png"
                    data-name="AVENGER 160"
                    data-description="The Avenger 160 offers the perfect blend of style and comfort for urban cruising.">AVENGER 160</button>
          </div>

          <!-- Discover Models -->
          <div class="model-group discover-models hidden">
            <button class="variant-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent" 
                    data-model="discover-125"
                    data-image="./assets/bikes/discover/discover_125.png"
                    data-name="DISCOVER 125"
                    data-description="The Discover 125 is designed for efficient urban commuting with its fuel-efficient engine and comfortable riding position.">DISCOVER 125</button>
            <button class="variant-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent" 
                    data-model="discover-110"
                    data-image="./assets/bikes/discover/discover_110.png"
                    data-name="DISCOVER 110"
                    data-description="The Discover 110 offers excellent fuel efficiency and low maintenance costs for daily commuting.">DISCOVER 110</button>
          </div>

          <!-- Platina Models -->
          <div class="model-group platina-models hidden">
            <button class="variant-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent" 
                    data-model="platina-110"
                    data-image="./assets/bikes/platina/platina_110.png"
                    data-name="PLATINA 110"
                    data-description="The Platina 110 is Bajaj's most fuel-efficient motorcycle, perfect for budget-conscious riders.">PLATINA 110</button>
            <button class="variant-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent" 
                    data-model="platina-100"
                    data-image="./assets/bikes/platina/platina_100.png"
                    data-name="PLATINA 100"
                    data-description="The Platina 100 offers unbeatable fuel efficiency and low maintenance costs for daily commuting.">PLATINA 100</button>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Own Your Dream Bajaj Section -->
  <section class="py-16 dream-bajaj-section">
    <div class="max-w-7xl mx-auto px-6">
      <div class="grid lg:grid-cols-2 gap-12 items-center">
        <!-- Image Section -->
        <div class="relative">
          <div class="rounded-2xl overflow-hidden shadow-2xl dream-bajaj-image">
            <img src="/assets/dream-section-img.png" alt="Bajaj Motorcycle with Customer"
              class="w-full h-[400px] object-cover" />
          </div>
        </div>

        <!-- Content Section -->
        <div class="space-y-6">
          <h2 class="text-4xl font-bold text-gray-900 leading-tight">
            OWN YOUR DREAM BAJAJ <br />
            MOTORCYCLE WITH EASE
          </h2>

          <p class="text-lg text-gray-600 leading-relaxed">
            At Bajaj Nepal, we believe that your dream ride should be within
            reach. Our simplified financing options are designed to make
            motorcycle ownership accessible and hassle-free for everyone.
          </p>

          <div class="pt-4">
            <button
              class="loan-availability-btn bg-[#222222] h-10 flex items-center text-white px-8 py-4 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
              Check Your Loan Availability
              <svg class="w-6 h-6 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor"
                viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Blog Section -->
  <section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Section Header -->
      <div class="text-center mb-12">
        <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Latest News & Stories</h2>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">
          Stay updated with the latest motorcycle news, riding tips, and community stories from the world of Bajaj.
        </p>
      </div>

      <!-- Blog Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Blog Card 1 -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
          <img src="assets/blog/blog-1.jpg" alt="Blog Post 1" class="w-full h-48 object-cover">
          <div class="p-6">
            <h3 class="text-xl font-semibold text-gray-900 mb-2">The Future of Motorcycle Technology</h3>
            <p class="text-gray-600 mb-4">Discover how cutting-edge technology is shaping the future of motorcycle riding and safety.</p>
            <a href="single-blog.html" class="text-blue-600 font-medium hover:text-blue-800">Read More →</a>
          </div>
        </div>

        <!-- Blog Card 2 -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
          <img src="assets/blog/blog-2.jpg" alt="Blog Post 2" class="w-full h-48 object-cover">
          <div class="p-6">
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Essential Motorcycle Maintenance Tips</h3>
            <p class="text-gray-600 mb-4">Learn the key maintenance practices to keep your motorcycle running smoothly and safely.</p>
            <a href="single-blog.html" class="text-blue-600 font-medium hover:text-blue-800">Read More →</a>
          </div>
        </div>

        <!-- Blog Card 3 -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
          <img src="assets/blog/blog-3.jpg" alt="Blog Post 3" class="w-full h-48 object-cover">
          <div class="p-6">
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Adventure Riding Guide</h3>
            <p class="text-gray-600 mb-4">Explore the best routes and tips for an unforgettable motorcycle adventure.</p>
            <a href="single-blog.html" class="text-blue-600 font-medium hover:text-blue-800">Read More →</a>
          </div>
        </div>
      </div>

      <!-- View All Blogs Button -->
      <div class="text-center mt-12">
        <a href="blogs.html"
          class="inline-block bg-gray-900 text-white px-8 py-3 rounded-full font-semibold hover:bg-gray-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
          View All Stories
        </a>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-gray-50 min-h-screen flex flex-col">
    <!-- Email Signup Section -->
    <div class="flex-1 flex items-center justify-center px-4 py-12">
      <div class="max-w-md w-full">
        <div class="text-center mb-8">
          <h2 class="text-2xl font-bold text-gray-900 mb-2">
            Sign up for Email
          </h2>
          <p class="text-sm text-gray-500 mb-1">
            Read our
            <a href="#" class="text-blue-500 underline">privacy policy</a>
            to learn about data processing
          </p>
          <p class="text-sm text-gray-500">
            Sign up for BAJAJ latest news and updates
          </p>
        </div>

        <form id="emailForm" class="mb-4">
          <div class="flex gap-2 mb-2">
            <input type="email" id="email" placeholder="YOUR EMAIL ADDRESS"
              class="flex-1 bg-white border border-gray-300 rounded-md px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              required />
            <button type="submit"
              class="bg-blue-500 text-white px-6 py-3 rounded-md text-sm font-medium hover:bg-blue-600">
              SUBSCRIBE NOW
            </button>
          </div>
          <p class="text-xs text-gray-500 text-center">
            This site is protected by reCAPTCHA and the Google
            <a href="#" class="underline">Privacy Policy</a> and
            <a href="#" class="underline">Terms of Service</a> apply.
          </p>
        </form>
      </div>
    </div>

    <!-- Footer Section -->
    <div class="bg-gray-900 text-white py-12">
      <div class="max-w-6xl mx-auto px-4">
        <div class="text-center mb-8">
          <div class="flex justify-center items-center mb-4">
            <div class="w-8 h-8 mr-3 bg-white rounded-full flex items-center justify-center">
              <span class="text-black font-bold text-sm">G</span>
            </div>
            <h3 class="text-lg font-medium">
              GOLCHHA GROUP WITH LEGACY OF 100 YEAR
            </h3>
          </div>
        </div>

        <!-- Footer Links -->
        <div class="flex justify-center gap-8 text-sm mb-8">
          <a href="#" class="hover:text-gray-300">TERMS OF USE</a>
          <a href="#" class="hover:text-gray-300">PRIVACY INFORMATION</a>
          <a href="#" class="hover:text-gray-300">COOKIES INFORMATION</a>
        </div>

        <!-- Copyright -->
        <div class="text-center text-xs text-gray-400 mb-8">
          <p>
            Copyright © 2025 Bajaj Auto Ltd – A Sole Shareholder Company - A
            Company subject to the Management and Coordination
          </p>
          <p>activities of BAJAJ AUTO. All rights reserved. VAT NO.</p>
        </div>

        <!-- Bottom Section -->
        <div class="flex flex-wrap justify-between items-center gap-4">
          <!-- Bajaj Logo -->
          <div>
            <img src="../assets/footer-logo.svg" alt="" />
          </div>

          <!-- Social Media Icons -->
          <div class="flex gap-4">
            <a href="#" class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1">
              <i class="fab fa-instagram"></i>
            </a>
            <a href="#" class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1">
              <i class="fab fa-facebook"></i>
            </a>
            <a href="#" class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1">
              <i class="fab fa-youtube"></i>
            </a>
            <a href="#" class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1">
              <i class="fab fa-tiktok"></i>
            </a>
            <a href="#" class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1">
              <i class="fab fa-twitter"></i>
            </a>
            <a href="#" class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1">
              <i class="fab fa-linkedin"></i>
            </a>
          </div>

          <!-- International Website -->
          <div class="flex items-center text-sm text-gray-400">
            <i class="fas fa-globe mr-2"></i>
            <a href="#" class="hover:text-white">International website</a>
          </div>
        </div>
      </div>
    </div>
  </footer>

  <script>
    document
      .getElementById("emailForm")
      .addEventListener("submit", function (e) {
        e.preventDefault();
        const email = document.getElementById("email").value;
        if (email) {
          alert(
            "Thank you for subscribing! You will receive updates at: " + email
          );
          document.getElementById("email").value = "";
        }
      });
  </script>
  <script type="module" src="js/bike-init.js"></script>
  <script type="module" src="js/main.js"></script>
  <script type="module" src="js/expereience-carousel.js"></script>
  <script type="module" src="js/blog-section.js"></script>
  <!-- Scripts -->
  <script>
    // Navbar Functionality
    document.addEventListener('DOMContentLoaded', () => {
      // Desktop Dropdown Functionality
      function toggleDropdown(dropdownId) {
        const dropdown = document.getElementById(dropdownId + '-dropdown');
        const arrow = document.getElementById(dropdownId + '-arrow');

        if (!dropdown || !arrow) return;

        // Close all other dropdowns
        document.querySelectorAll('.dropdown-content, .media-dropdown-content').forEach(dd => {
          if (dd !== dropdown) {
            dd.classList.remove('show');
            dd.classList.add('hidden');
          }
        });

        // Toggle current dropdown
        if (dropdown.classList.contains('hidden')) {
          dropdown.classList.remove('hidden');
          dropdown.classList.add('show');
          arrow.style.transform = 'rotate(180deg)';
        } else {
          dropdown.classList.remove('show');
          dropdown.classList.add('hidden');
          arrow.style.transform = 'rotate(0deg)';
        }
      }

      // Brand Filtering
      function filterByBrand(brand) {
        // Update active brand
        document.querySelectorAll('.brand-item').forEach(item => {
          item.classList.toggle('active', item.dataset.brand === brand);
        });

        // Show/hide motorcycle sections
        document.querySelectorAll('.motorcycle-section').forEach(section => {
          section.classList.toggle('active', section.dataset.brand === brand);
        });

        // Reset category filter to 'All'
        filterByCategory('All');
      }

      // Category Filtering
      function filterByCategory(category) {
        // Update active category button
        document.querySelectorAll('.category-btn').forEach(btn => {
          btn.classList.toggle('active', btn.dataset.category === category);
        });

        // Show/hide category sections
        document.querySelectorAll('.category-section').forEach(section => {
          if (category === 'All') {
            section.style.display = 'block';
          } else {
            section.style.display = section.dataset.category === category ? 'block' : 'none';
          }
        });
      }

      // Mobile Menu Functionality
      const mobileMenu = {
        menu: document.getElementById('mobile-menu'),
        bikesDropdown: document.getElementById('mobile-bikes-dropdown'),
        menuBtn: document.getElementById('mobile-menu-btn'),
        bikesBtn: document.getElementById('mobile-bikes-btn'),
        closeBtns: document.querySelectorAll('.close-btn'),
        backBtns: document.querySelectorAll('.back-btn'),

        init() {
          // Mobile menu toggle
          this.menuBtn?.addEventListener('click', () => this.toggleMenu());
          this.bikesBtn?.addEventListener('click', () => this.toggleBikesDropdown());

          // Close buttons
          this.closeBtns.forEach(btn => {
            btn.addEventListener('click', () => {
              this.menu.classList.add('translate-x-full');
              this.bikesDropdown.classList.add('translate-x-full');
            });
          });

          // Back buttons
          this.backBtns.forEach(btn => {
            btn.addEventListener('click', () => {
              this.bikesDropdown.classList.add('translate-x-full');
            });
          });

          // Brand items in mobile view
          document.querySelectorAll('.mobile-brand-item').forEach(item => {
            item.addEventListener('click', () => {
              const brand = item.querySelector('span').textContent;
              filterByBrand(brand);
              this.bikesDropdown.classList.add('translate-x-full');
            });
          });
        },

        toggleMenu() {
          this.menu.classList.toggle('translate-x-full');
          if (!this.menu.classList.contains('translate-x-full')) {
            this.bikesDropdown.classList.add('translate-x-full');
          }
        },

        toggleBikesDropdown() {
          this.bikesDropdown.classList.toggle('translate-x-full');
          if (!this.bikesDropdown.classList.contains('translate-x-full')) {
            this.menu.classList.add('translate-x-full');
          }
        }
      };

      // Initialize Event Listeners
      function initEventListeners() {
        // Desktop dropdowns
        document.querySelectorAll('.dropdown button').forEach(btn => {
          btn.addEventListener('click', (e) => {
            const dropdownId = e.target.closest('.dropdown').querySelector('button').textContent.trim().toLowerCase();
            toggleDropdown(dropdownId);
          });
        });

        // Brand filtering
        document.querySelectorAll('.brand-item').forEach(item => {
          item.addEventListener('click', (e) => {
            e.preventDefault();
            filterByBrand(item.dataset.brand);
          });
        });

        // Category filtering
        document.querySelectorAll('.category-btn').forEach(btn => {
          btn.addEventListener('click', () => {
            filterByCategory(btn.dataset.category);
          });
        });

        // Initialize mobile menu
        mobileMenu.init();
      }

      // Initialize everything
      initEventListeners();
    });
  </script>
</body>

</html>