<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Blog;

class BlogController extends Controller
{
    /**
     * Display the blogs listing page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $featuredBlogs = Blog::published()->featured()->latest('published_at')->take(3)->get();
        $blogs = Blog::published()->latest('published_at')->paginate(9);

        return view('blogs.index', compact('featuredBlogs', 'blogs'));
    }

    /**
     * Display a specific blog post.
     *
     * @param string $slug
     * @return \Illuminate\View\View
     */
    public function show($slug)
    {
        $blog = Blog::where('slug', $slug)->published()->firstOrFail();
        $relatedBlogs = Blog::published()
            ->where('id', '!=', $blog->id)
            ->where('category', $blog->category)
            ->latest('published_at')
            ->take(3)
            ->get();

        return view('blogs.show', compact('blog', 'relatedBlogs'));
    }

    /**
     * Get blogs data for AJAX requests.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBlogsData()
    {
        $blogs = Blog::published()->latest('published_at')->get();

        return response()->json([
            'success' => true,
            'data' => $blogs
        ]);
    }
}
