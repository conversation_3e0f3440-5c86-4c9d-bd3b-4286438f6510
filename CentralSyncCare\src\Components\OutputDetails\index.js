import { Close } from "@mui/icons-material";
import { Autocomplete, Button, CircularProgress, FormControl, FormControlLabel, Grid, IconButton, InputLabel, MenuItem, Radio, RadioGroup, Select, TextField } from "@mui/material";
import React, { useEffect, useState } from "react";

const OutputDetails = (props) => {

    const [categoryValue, setCategoryValue] = useState([]);
    const [productValue, setProductValue] = useState([]);

    // useEffect(() => {
    //     if (props.outputDetails?.length > 0) {
    //         const categories = props.outputDetails.map(detail => detail.value?.[0]?.categoryValue || []).flat();
    //         const products = props.outputDetails.map(detail => detail.value?.[0]?.productValue || []).flat();

    //         setCategoryValue(categories);
    //         setProductValue(products);
    //     }
    // }, [props.outputDetails]);

    useEffect(() => {
        if (props.outputDetails?.length > 0) {
            const categories = props.outputDetails.map(detail => typeof detail.value === "string" ? detail.value : detail.value?.length > 0 && detail.value?.map((m) => m.categoryValue || []).flat() || []).flat();
            const products = props.outputDetails.map(detail => typeof detail.value === "string" ? detail.value : detail.value?.length > 0 && detail.value?.map((m) => m.productValue || []).flat() || []).flat();

            setCategoryValue(categories);
            setProductValue(products);
        }
    }, [props.outputDetails]);

    const handleCategoryChange = (index, newCategoryValue, code) => {
        props.setCode(code);
        const categoryIds = newCategoryValue.map((item) => item.id);
        setCategoryValue(categoryIds);
        props.getProductListFilter(categoryIds)
        props.updateOutputDetail(index, "value", [{
            categoryValue: categoryIds,
        }]);
    };

    const handleProductChange = (index, newProductValue, code) => {
        props.setCode(code);
        const productIds = newProductValue.map((item) => item.id);
        setProductValue(productIds);
        props.updateOutputDetail(index, "value", [{
            categoryValue,
            productValue: productIds,
        }]);
    };

    return (
        <div>
            <h4>Output Details</h4>
            {props.outputDetails.map((detail, index) => (
                <Grid container spacing={2} key={index}>
                    <Grid item xs={12} md={4}>
                        <div style={{ marginBottom: "20px" }}>
                            {index > 0 && (
                                <div style={{ marginBottom: "10px" }}>
                                    <h4 style={{ margin: 0, fontWeight: "inherit" }}>Condition</h4>
                                    <FormControl>
                                        <RadioGroup
                                            row
                                            aria-labelledby={`condition-radio-buttons-${index}`}
                                            name={`condition-${index}`}
                                            value={detail.condition}
                                            onChange={(e) =>
                                                props.updateOutputDetail(index, "condition", e.target.value, "")
                                            }
                                        >
                                            <FormControlLabel value="and" control={<Radio />} label="AND" />
                                            <FormControlLabel value="or" control={<Radio />} label="OR" />
                                        </RadioGroup>
                                    </FormControl>
                                </div>
                            )}

                            <FormControl fullWidth>
                                <InputLabel>{props.outputListLoading ? "Please Wait.." : "Output Type"}</InputLabel>
                                <Select
                                    value={detail.input_output_id}
                                    label={props.outputListLoading ? "Please Wait.." : "Output Type"}
                                    onChange={(e) =>
                                        props.updateOutputDetail(index, "input_output_id", e.target.value, "")
                                    }
                                    renderValue={(selected) => {
                                        const selectedItem = props.outputData.find(item => item.id === selected);
                                        return selectedItem ? selectedItem.name : selected;
                                    }}
                                >
                                    <MenuItem value="">Select Output</MenuItem>
                                    {!props.view && props.outputData.map((item) => (
                                        <MenuItem
                                            key={item.id}
                                            value={item.id}
                                            disabled={props.outputDetails.some(
                                                (output, idx) => output.input_output_id === item.id && idx !== index
                                            )}
                                        >
                                            {item.name}
                                        </MenuItem>
                                    ))}
                                </Select>
                                {props.outputListLoading && (
                                    <CircularProgress
                                        size={24}
                                        style={{
                                            position: "absolute",
                                            top: "50%",
                                            right: 16,
                                            marginTop: -12,
                                            marginRight: 10,
                                        }}
                                    />
                                )}
                            </FormControl>
                        </div>
                    </Grid>

                    <Grid item xs={12} md={8}>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                            {detail.input_output_id && (
                                <div style={{ marginTop: index > 0 ? "75px" : "0px" }}>
                                    {(() => {
                                        const selectedOutput = props.outputData.find(
                                            (item) => item.id === parseInt(detail.input_output_id)
                                        );

                                        if (selectedOutput?.input_type === "text" && selectedOutput.is_value_required) {
                                            return (
                                                <TextField
                                                    fullWidth
                                                    sx={{ minWidth: "400px" }}
                                                    required
                                                    label="Output Value"
                                                    placeholder={selectedOutput.symbol || ""}
                                                    value={detail.value}
                                                    onChange={(e) =>
                                                        props.updateOutputDetail(index, "value", e.target.value, "")
                                                    }
                                                />
                                            );
                                        }

                                        if (selectedOutput?.input_type === "list" && selectedOutput?.input_output_code === "GFIFCOLI" && selectedOutput.is_value_required) {
                                            return (
                                                <Grid container spacing={2}>
                                                    {/* Category Selection */}
                                                    <Grid item xs={6}>
                                                        <FormControl fullWidth>
                                                            <Autocomplete
                                                                multiple
                                                                options={props.productCategoryList || []}
                                                                getOptionLabel={(option) => option.name || ""}
                                                                isOptionEqualToValue={(option, value) => option.id === value.id}
                                                                value={props.productCategoryList.filter(cat => categoryValue.includes(cat.id))}
                                                                onChange={(event, newValue) => handleCategoryChange(index, newValue, "GFIFCOLI")}
                                                                loading={props.productCategoryLoading}
                                                                renderInput={(params) => (
                                                                    <TextField
                                                                        {...params}
                                                                        label={props.productCategoryLoading ? "Please Wait..." : "Select Category"}
                                                                        sx={{ minWidth: "300px" }}
                                                                        InputProps={{
                                                                            ...params.InputProps,
                                                                            endAdornment: (
                                                                                <>
                                                                                    {props.productCategoryLoading ? <CircularProgress color="inherit" size={20} /> : null}
                                                                                    {params.InputProps.endAdornment}
                                                                                </>
                                                                            ),
                                                                        }}
                                                                    />
                                                                )}
                                                            />
                                                        </FormControl>
                                                    </Grid>

                                                    {/* Product Selection */}
                                                    <Grid item xs={6}>
                                                        <FormControl fullWidth>
                                                            <Autocomplete
                                                                multiple
                                                                options={props.productList || []}
                                                                getOptionLabel={(option) => option.name || ""}
                                                                isOptionEqualToValue={(option, value) => option.id === value.id}
                                                                value={props.productList.filter(prod => productValue.includes(prod.id))}
                                                                onChange={(event, newValue) => handleProductChange(index, newValue, "GFIFCOLI")}
                                                                loading={props.productListLoading}
                                                                renderInput={(params) => (
                                                                    <TextField
                                                                        {...params}
                                                                        label={props.productListLoading ? "Please Wait..." : "Select Products"}
                                                                        sx={{ minWidth: "300px" }}
                                                                        InputProps={{
                                                                            ...params.InputProps,
                                                                            endAdornment: (
                                                                                <>
                                                                                    {props.productListLoading ? <CircularProgress color="inherit" size={20} /> : null}
                                                                                    {params.InputProps.endAdornment}
                                                                                </>
                                                                            ),
                                                                        }}
                                                                    />
                                                                )}
                                                            />
                                                        </FormControl>
                                                    </Grid>
                                                </Grid>
                                            );
                                        }
                                        if (selectedOutput?.input_type === "list" && selectedOutput.is_value_required) {
                                            return (
                                                <div>
                                                    <FormControl fullWidth>
                                                        <Autocomplete
                                                            multiple
                                                            options={!props.view ? props.productList || [] : []}
                                                            getOptionLabel={(option) => option.name || ""}
                                                            isOptionEqualToValue={(option, value) => option.id == value.id}
                                                            // value={detail.value
                                                            //     ? props.productList.filter(item => detail.value.includes(item.id))
                                                            //     : []
                                                            // }
                                                            value={productValue
                                                                ? props.productList.filter(item => productValue.includes(item.id))
                                                                : []
                                                            }
                                                            onChange={(event, newValue) =>
                                                                props.updateOutputDetail(index, "value", newValue.map(item => item.id), "")
                                                            }
                                                            loading={props.productListLoading}
                                                            renderInput={(params) => (
                                                                <TextField
                                                                    {...params}
                                                                    label={props.productListLoading ? "Please Wait..." : "Select Values"}
                                                                    sx={{ minWidth: "400px" }}
                                                                    InputProps={{
                                                                        ...params.InputProps,
                                                                        endAdornment: (
                                                                            <>
                                                                                {props.productListLoading ? (
                                                                                    <CircularProgress color="inherit" size={20} />
                                                                                ) : null}
                                                                                {params.InputProps.endAdornment}
                                                                            </>
                                                                        ),
                                                                    }}
                                                                />
                                                            )}
                                                        />
                                                    </FormControl>

                                                </div>
                                            );
                                        }
                                    })()}
                                </div>
                            )}
                            {!props.view && props.outputDetails.length > 1 && (
                                <IconButton
                                    style={{ marginLeft: "20px" }}
                                    onClick={() => props.removeOutputDetail(index)}
                                >
                                    <Close color={"error"} />
                                </IconButton>
                            )}
                        </div>
                    </Grid>
                </Grid>
            ))}

            {/* {!props.view && <Button variant={"contained"} onClick={props.addOutputDetail} disabled={props.isAddButtonDisabled}>+ Add Output</Button>} */}
        </div>

    );
};

export default OutputDetails;
