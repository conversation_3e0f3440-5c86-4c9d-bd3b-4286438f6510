<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Bike;

class BikeController extends Controller
{
    /**
     * Display a specific bike details page.
     *
     * @param string $slug
     * @return \Illuminate\View\View
     */
    public function show($slug)
    {
        $bike = Bike::where('slug', $slug)->active()->firstOrFail();

        return view('bikes.show', compact('bike'));
    }

    /**
     * Get single bike data for AJAX requests.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBikeData($id)
    {
        $bike = Bike::active()->find($id);

        if (!$bike) {
            return response()->json([
                'success' => false,
                'message' => 'Bike not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $bike
        ]);
    }

    /**
     * Get bikes by brand for AJAX requests.
     *
     * @param string $brand
     * @return \Illuminate\Http\JsonResponse
     */
    public function getByBrand($brand)
    {
        $bikes = Bike::active()->byBrand($brand)->get();

        return response()->json([
            'success' => true,
            'data' => $bikes
        ]);
    }
}
