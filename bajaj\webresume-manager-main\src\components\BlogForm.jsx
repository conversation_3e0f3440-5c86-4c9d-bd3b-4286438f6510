"use client";

import { deleteBlog, getBlogById } from "@/actions/blog";
import { Button } from "@/components/ui/button";
import { useSession } from "next-auth/react";
import { CldUploadWidget } from "next-cloudinary";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";

const BlogForm = ({ blogId }) => {
  const router = useRouter();
  const [errors, setErrors] = useState({});
  const [success, setSuccess] = useState(false);
  const { data: session, status } = useSession();

  useEffect(() => {
    if (success) {
      blogId
        ? toast.success("Blog updated successfully!")
        : toast.success("Blog created successfully!");
      router.push("/blogs");
    }
  }, [success, router]);

  //handling form data with image
  const [img, setImg] = useState("");
  const [formData, setFormData] = useState({
    title: "",
    category: "choose",
    desc: "",
  });

  //get details of existing blog in case of edit
  useEffect(() => {
    const getBlog = async () => {
      if (blogId) {
        const blog = await getBlogById(blogId);
        setFormData({
          title: blog.title,
          category: blog.category,
          desc: blog.desc,
        });
        setImg(blog.image);
      }
    };

    getBlog();
  }, [blogId]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    const userId = session.user.userId;
    //console.log(session);
    const res = await fetch(`/api/blog/${userId}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        title: formData.title,
        category: formData.category,
        desc: formData.desc,
        image: img || "",
      }),
    });

    const data = await res.json();
    if (res.ok) {
      //console.log("Post created:", data);
      setFormData({
        title: "",
        desc: "",
        category: "choose",
      });
      setImg("");
      setSuccess(true);
      setErrors({});

      //delete old blog after updating
      if (blogId) {
        await deleteBlog(blogId);
      }
    } else {
      //console.log("errror", data);
      setErrors(data.error || { general: [data.error] });
      setSuccess(false);
    }
  };

  if (status === "loading") return <p>Loading...</p>;

  return (
    <div className="px-4 md:px-8 xl:px-16">
      <div className="flex items-center gap-16 mb-12">
        <h1 className="text-3xl font-semibold">
          {blogId ? "Update Blog Post" : "Add New Blog Post"}
        </h1>
      </div>

      <form onSubmit={handleSubmit} className="flex flex-col gap-6">
        <div className="flex flex-col">
          <label htmlFor="title">Title</label>
          <input
            type="text"
            name="title"
            placeholder="Input Blog TItle"
            value={formData.title}
            onChange={(e) =>
              setFormData({
                ...formData,
                title: e.target.value,
              })
            }
            className="border border-gray-400 px-2 py-1 outline-none"
            required
          />
          {errors.title && <p className="text-red-500">{errors.title[0]}</p>}
        </div>

        <div className="flex flex-col">
          <label htmlFor="category">Category</label>
          <select
            id="category"
            name="category"
            value={formData.category}
            onChange={(e) =>
              setFormData({
                ...formData,
                category: e.target.value,
              })
            }
            className="border border-gray-400 px-2 py-1 outline-none"
            required
          >
            <option disabled hidden value="choose">
              Choose category
            </option>
            <option value="technology">technology</option>
            <option value="lifestyle">lifestyle</option>
            <option value="travel">travel</option>a
            <option value="food">food</option>
          </select>
          {errors.category && (
            <p className="text-red-500">{errors.category[0]}</p>
          )}
        </div>

        <CldUploadWidget
          uploadPreset="portfolio-backend"
          onSuccess={(result) => setImg(result?.info.secure_url)}
        >
          {({ open }) => {
            return (
              <div className="flex flex-col">
                <label htmlFor="image">Image</label>
                <Image
                  src={img || "/no-image.png"}
                  width={140}
                  height={100}
                  alt="image"
                  className="w-20 h-auto rounded-lg"
                />
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    open();
                  }}
                  name="imageUrl"
                  className="border border-gray-400 px-2 py-1 outline-none cursor-pointer"
                >
                  Choose Image
                </button>
              </div>
            );
          }}
        </CldUploadWidget>

        <div className="flex flex-col">
          <label htmlFor="desc">Description</label>
          <textarea
            name="desc"
            cols="40"
            rows="8"
            value={formData.desc}
            onChange={(e) =>
              setFormData({
                ...formData,
                desc: e.target.value,
              })
            }
            placeholder="Write your blog description here"
            className="border border-gray-400 px-2 py-1 outline-none"
            required
          ></textarea>
          {errors.desc && <p className="text-red-500">{errors.desc[0]}</p>}
        </div>

        <Button
          type="submit"
          className="bg-gray-800 text-white cursor-pointer hover:text-stone-200 mb-10"
        >
          {blogId ? "Update" : "Submit"}
        </Button>
      </form>
    </div>
  );
};

export default BlogForm;
