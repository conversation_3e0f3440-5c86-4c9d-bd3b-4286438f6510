import { AddBox, IndeterminateCheckBox, Visibility, VisibilityOff } from "@mui/icons-material";
import {
    Box,
    Button,
    Checkbox,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    FormControl,
    FormControlLabel,
    FormLabel,
    Grid,
    IconButton,
    InputAdornment,
    Radio,
    RadioGroup,
    styled,
    TextField,
} from "@mui/material";
import { SimpleTreeView, TreeItem, treeItemClasses } from "@mui/x-tree-view";
import { useEffect, useState } from "react";
import * as React from 'react';


const StyledHeaderTitle = styled(DialogTitle)(({ theme }) => ({
    background: theme.palette.primary.main,
    color: "#fff",
    position: "relative",
    "& button": {
        position: "absolute",
        right: "15px",
        top: "15px",
        color: "#fff",
    },
}));

const CustomTreeItem = styled(TreeItem)(({ theme }) => ({
    [`& .${treeItemClasses.iconContainer}`]: {
        '& .close': {
            opacity: 0.3,
        },
    },
}));

const EditDialog = (props) => {
    const [dialogDetails, setDialogDetails] = useState({
        open: true,
        success: false,
    });
    const [errors, setErrors] = useState({});
    const [showPassword, setShowPassword] = useState(false);  // State for visibility toggle
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [formData, setFormData] = useState({
        name: props.viewDetails.name ? props.viewDetails.name : '',
        base_url: props.viewDetails.base_url ? props.viewDetails.base_url : '',
        create_project_admin: "0",
        admin_name: "",
        admin_email: "",
        admin_password: "",
        modulePermissions: [],
        // id: '',
        // email: '',
        // address: '',
        // phone: '',
    });

    const [userData, setUserData] = useState({
        full_name: '',
        email: '',
        password: '',
        confirm_password: '',
    });
    const [value, setValue] = React.useState('0');

    const handleChangeRadio = (event) => {
        setValue(event.target.value);
    };

    // useEffect(() => {
    //     if (props.viewDetails) {
    //         setFormData({
    //             name: props.viewDetails.name || '',
    //             base_url: props.viewDetails.base_url || '',

    //         });
    //     }
    // }, [props.viewDetails]);
    const [checked, setChecked] = useState(() =>
        props.viewDetails.modulePermission?.reduce((acc, module) => {
            acc[module.id] = module.status === 1 ? true : false;
            if (module.sub_modules) {
                module.sub_modules.forEach((sub) => {
                    acc[sub.id] = sub.status === 1 ? true : false;
                });
            }
            return acc;
        }, {})
    );

    useEffect(() => {
        if (props.viewDetails) {
            const formatPermissions = (permissions) => {
                return Object.entries(permissions).map(([key, value]) => ({
                    id: Number(key),
                    status: value ? 1 : 0,
                }));
            };
            let name = formData.name
            let base_url = formData.base_url
            let full_name = userData.full_name
            let email = userData.email
            let password = userData.password
            setFormData({
                name: name,
                base_url: base_url,
                create_project_admin: value,
                admin_name: full_name,
                admin_email: email,
                admin_password: password,
                modulePermissions: formatPermissions(checked || {}),
            });
        }
    }, [props.viewDetails, userData, checked]);


    useEffect(() => {
        props.sendEdit(dialogDetails, formData);
    }, [dialogDetails]);


    const handleClose = () => {
        setDialogDetails({
            ...dialogDetails,
            open: false,
        });
    };

    const handleYes = () => {
        if (validate()) {
            setDialogDetails({
                ...dialogDetails,
                open: false,
                success: true,

            });
        }
    };

    const validate = () => {
        const newErrors = {};
        if (!formData.name) newErrors.name = 'Project Name is required';
        if (!formData.base_url) newErrors.name = 'Project Base Url is required';
        if (value === "1") {
            if (!userData.full_name) newErrors.full_name = 'Name is required';
            if (!userData.email) newErrors.email = 'Email is required';
            if (!userData.password) {
                newErrors.password = 'Password is required';
            } else if (userData.password.length < 6) {
                newErrors.password = 'Password should be at least 6 characters';
            }
            if (!userData.confirm_password) {
                newErrors.confirm_password = 'Confirm Password is required';
            } else if (userData.confirm_password !== userData.password) {
                newErrors.confirm_password = 'Passwords do not match';
            }
        }
        // if (!formData.id) newErrors.id = 'Project ID is required';
        // if (!formData.email) newErrors.email = 'Project Email is required';
        // if (formData.phone && formData.phone.length > 10) newErrors.phone = 'Phone number should be of 10 digits.';
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };


    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData((prevData) => ({
            ...prevData,
            [name]: value,
        }));
    };

    const handleChangeUser = (e) => {
        const { name, value } = e.target;
        setUserData((prevData) => ({
            ...prevData,
            [name]: value,
        }));
        if (name === "confirm_password" && value !== "" && value !== userData.password) {
            setErrors((prev) => ({
                ...prev,
                confirm_password: "Passwords do not match",
            }));
        } else {
            setErrors((prev) => ({
                ...prev,
                confirm_password: "",
            }));
        }
    };

    const handleCheck = (id, subModules = []) => {
        setChecked((prev) => {
            const newChecked = { ...prev };

            if (subModules.length > 0) {
                const newValue = !newChecked[id];
                newChecked[id] = newValue;
                subModules.forEach((sub) => (newChecked[sub.id] = newValue));
            } else {

                newChecked[id] = !newChecked[id];


                props.viewDetails.modulePermission.forEach((module) => {
                    if (module.sub_modules?.some((sub) => sub.id === id)) {
                        const allChecked = module.sub_modules.every((sub) => newChecked[sub.id]);
                        const noneChecked = module.sub_modules.every((sub) => !newChecked[sub.id]);

                        newChecked[module.id] = allChecked
                            ? true
                            : noneChecked
                                ? false
                                : newChecked[module.id];
                    }
                });
            }

            return newChecked;
        });
    };

    const isIndeterminate = (subModules) =>
        subModules.some((sub) => checked[sub.id]) &&
        !subModules.every((sub) => checked[sub.id]);

    const renderTreeItems = (modules) =>

        modules.map((module) => {
            const subModules = module.sub_modules || [];
            const isChecked = checked[module.id];
            const indeterminate = isIndeterminate(subModules);

            return (
                <CustomTreeItem
                    key={module.id}
                    itemId={module.id.toString()}
                    label={
                        <Box display="flex" alignItems="center">
                            <Checkbox
                                checked={isChecked}
                                indeterminate={indeterminate}
                                onChange={() => handleCheck(module.id, subModules)}
                            />
                            {module.name}
                        </Box>
                    }
                >
                    {subModules.length > 0 && renderTreeItems(subModules)}
                </CustomTreeItem>
            );
        });

    return (
        <div>
            <Dialog
                open={dialogDetails.open}
                onClose={handleClose}
                maxWidth="md"
                fullWidth
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <StyledHeaderTitle id="alert-dialog-title">
                    {props.viewDetails.id ? `Edit Project` : `Add Project`}
                </StyledHeaderTitle>
                <DialogContent>
                    <Box pt={3}>
                        <TextField
                            required
                            label="Project Name"
                            name="name"
                            value={formData.name}
                            onChange={handleChange}
                            error={!!errors.name}
                            helperText={errors.name}
                            fullWidth
                            margin="normal"
                        />

                        <TextField
                            required
                            label="Project Base Url"
                            name="base_url"
                            value={formData.base_url}
                            onChange={handleChange}
                            error={!!errors.base_url}
                            helperText={errors.base_url}
                            fullWidth
                            margin="normal"
                        />

                        {/* <TextField
                            required
                            label="Project ID"
                            name="id"
                            type="number"
                            value={formData.id}
                            onChange={handleChange}
                            error={!!errors.id}
                            helperText={errors.id}
                            fullWidth
                            margin="normal"
                        />
                        <TextField
                            required
                            label="Project Email"
                            name="email"
                            type="email"
                            value={formData.email}
                            onChange={handleChange}
                            error={!!errors.email}
                            helperText={errors.email}
                            fullWidth
                            margin="normal"
                        />
                        <TextField
                            label="Project Address"
                            name="address"
                            value={formData.address}
                            onChange={handleChange}
                            fullWidth
                            margin="normal"
                        />
                        <TextField
                            label="Project Phone"
                            name="phone"
                            value={formData.phone}
                            onChange={handleChange}
                            error={!!errors.phone}
                            helperText={errors.phone}
                            fullWidth
                            margin="normal"
                        /> */}
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                                <h4>Module Permission</h4>
                                <Box p={2} sx={{
                                    boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)"
                                }}>

                                    <SimpleTreeView
                                        defaultExpandedItems={['Shopify', 'Erply']}
                                        slots={{
                                            expandIcon: AddBox,
                                            collapseIcon: IndeterminateCheckBox,

                                        }}
                                    >
                                        {renderTreeItems(props.viewDetails.modulePermission, "module")}
                                    </SimpleTreeView>

                                </Box>
                            </Grid>
                        </Grid>

                        {props.viewDetails.id ? null : (
                            <>
                                <h4>Add Admin User</h4>
                                <FormControl>
                                    <RadioGroup
                                        row
                                        aria-labelledby="demo-controlled-radio-buttons-group"
                                        name="controlled-radio-buttons-group"
                                        value={value}
                                        onChange={handleChangeRadio}
                                    >
                                        <FormControlLabel value="0" control={<Radio />} label="No" />
                                        <FormControlLabel value="1" control={<Radio />} label="Yes" />
                                    </RadioGroup>
                                </FormControl>
                                {/* <Grid container spacing={2}> */}
                                    {/* <Grid item xs={12} md={6}>
                                        <h4>Module Permission</h4>
                                        <Box p={2} sx={{
                                            boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)"
                                        }}>

                                            <SimpleTreeView
                                                defaultExpandedItems={['Shopify', 'Erply']}
                                                slots={{
                                                    expandIcon: AddBox,
                                                    collapseIcon: IndeterminateCheckBox,

                                                }}
                                            >
                                                {renderTreeItems(props.viewDetails.modulePermission, "module")}
                                            </SimpleTreeView>

                                        </Box>
                                    </Grid> */}


                                    <Grid item xs={12} md={6}>

                                        {value === "1" &&
                                            <>
                                                <h4>User Details</h4>
                                                <Box p={2} sx={{
                                                    boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)"
                                                }}>
                                                    <TextField
                                                        required
                                                        label="Name"
                                                        name="full_name"
                                                        value={userData.full_name}
                                                        onChange={handleChangeUser}
                                                        error={!!errors.full_name}
                                                        helperText={errors.full_name}
                                                        fullWidth
                                                        margin="normal"
                                                    />
                                                    <TextField
                                                        required
                                                        label="Email"
                                                        name="email"
                                                        type="email"
                                                        value={userData.email}
                                                        onChange={handleChangeUser}
                                                        error={!!errors.email}
                                                        helperText={errors.email}
                                                        fullWidth
                                                        margin="normal"
                                                    />
                                                    <TextField
                                                        label="Password"
                                                        name="password"
                                                        type={showPassword ? "text" : "password"} // Toggle between text and password
                                                        value={userData.password}
                                                        onChange={handleChangeUser}
                                                        error={!!errors.password}
                                                        helperText={errors.password}
                                                        fullWidth
                                                        margin="normal"
                                                        autoComplete="new-password"
                                                        InputProps={{
                                                            endAdornment: (
                                                                <InputAdornment position="end">
                                                                    <IconButton
                                                                        onClick={() => setShowPassword(!showPassword)}
                                                                        edge="end"
                                                                    >
                                                                        {showPassword ? <VisibilityOff /> : <Visibility />}
                                                                    </IconButton>
                                                                </InputAdornment>
                                                            ),
                                                        }}
                                                    />
                                                    <TextField
                                                        label="Confirm Password"
                                                        name="confirm_password"
                                                        type={showConfirmPassword ? "text" : "password"}
                                                        value={userData.confirm_password}
                                                        onChange={handleChangeUser}
                                                        error={!!errors.confirm_password}
                                                        helperText={errors.confirm_password}
                                                        fullWidth
                                                        margin="normal"
                                                        InputProps={{
                                                            endAdornment: (
                                                                <InputAdornment position="end">
                                                                    <IconButton
                                                                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                                                        edge="end"
                                                                    >
                                                                        {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                                                                    </IconButton>
                                                                </InputAdornment>
                                                            ),
                                                        }}
                                                    />
                                                </Box>
                                            </>
                                        }
                                    </Grid>
                                {/* </Grid> */}
                            </>
                        )}
                    </Box>
                </DialogContent>
                <DialogActions styled={{ margin: "5px 10px" }}>
                    <Button onClick={handleClose} color="error" variant="contained" autoFocus>
                        Cancel
                    </Button>
                    <Button
                        onClick={handleYes}
                        color="primary"
                        variant="contained"
                        autoFocus
                    >
                        {props.viewDetails.id ? `Edit` : `Save`}
                    </Button>
                </DialogActions>
            </Dialog>
        </div>
    );
};

export default EditDialog;
