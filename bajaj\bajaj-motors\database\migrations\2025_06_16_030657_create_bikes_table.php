<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('bikes', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('brand')->default('bajaj');
            $table->text('description');
            $table->string('price');
            $table->string('image')->nullable();
            $table->json('gallery')->nullable(); // Array of image URLs
            $table->string('engine');
            $table->string('power');
            $table->string('torque')->nullable();
            $table->string('mileage');
            $table->string('top_speed')->nullable();
            $table->string('weight')->nullable();
            $table->string('fuel_capacity')->nullable();
            $table->json('features')->nullable(); // Array of features
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('bikes');
    }
};
