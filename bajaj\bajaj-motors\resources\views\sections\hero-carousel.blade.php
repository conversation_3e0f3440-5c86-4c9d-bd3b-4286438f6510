<!-- Hero Section with Carousel -->
<section class="relative h-screen overflow-hidden carousel-with-header">
    <!-- Carousel Container -->
    <div class="carousel-container relative w-full h-full">
        <!-- Carousel Slides -->
        <div id="carousel-slides" class="flex transition-transform duration-700 ease-in-out h-full">
            @if(isset($heroSlides) && count($heroSlides) > 0)
                @foreach($heroSlides as $slide)
                    <div class="carousel-slide w-full h-full flex-shrink-0 relative">
                        <img src="{{ $slide->image ?? $slide['image'] }}" 
                             alt="Hero Slide {{ $loop->iteration }}" 
                             class="w-full h-full object-cover" />
                    </div>
                @endforeach
            @else
                <!-- Default slide if no data -->
                <div class="carousel-slide w-full h-full flex-shrink-0 relative">
                    <img src="{{ asset('assets/hero_image_1.png') }}" 
                         alt="Hero Slide" 
                         class="w-full h-full object-cover" />
                </div>
            @endif
        </div>

        <!-- Navigation Arrows -->
        <button id="prevBtn" class="absolute left-6 top-1/2 transform -translate-y-1/2 bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white p-3 rounded-full transition-all duration-300 z-10 group">
            <svg class="w-6 h-6 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
        </button>
        <button id="nextBtn" class="absolute right-6 top-1/2 transform -translate-y-1/2 bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white p-3 rounded-full transition-all duration-300 z-10 group">
            <svg class="w-6 h-6 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
        </button>

        <!-- Explore More Button -->
        <button id="explore-more-btn" class="absolute bottom-24 h-10 right-24 bg-[#222222] text-white px-6 rounded-full font-semibold hover:bg-blue-600 transition-all duration-300 z-20 flex items-center space-x-2 group">
            <span>Explore More</span>
            <svg class="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
        </button>

        <!-- Bouncing Arrow Indicator -->
        <div id="bouncing-arrow" class="absolute bottom-20 left-1/2 transform -translate-x-1/2 z-15">
            <svg width="22" height="21" viewBox="0 0 22 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11 13.6001L21 9.20007L22 10.7001L11 20.2001L0 10.7001L0.999992 9.20007L11 13.6001Z" fill="#FAFAFA" />
                <path d="M11 4.80002L17.6 0.400024L18.7 1.50002L11 9.20002L3.30005 1.50002L4.40005 0.400024L11 4.80002Z" fill="#FAFAFA" />
            </svg>
        </div>

        <!-- Dot Indicators -->
        <div id="carousel-dots" class="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-3 z-10">
            @if(isset($heroSlides) && count($heroSlides) > 0)
                @foreach($heroSlides as $slide)
                    <button class="w-3 h-3 rounded-full transition-all duration-300 {{ $loop->first ? 'bg-white' : 'bg-white/50' }}"></button>
                @endforeach
            @else
                <button class="w-3 h-3 rounded-full bg-white"></button>
            @endif
        </div>

        <!-- Overlay for better text readability -->
        <div class="absolute inset-0 bg-black/20 z-0"></div>
    </div>
</section>
