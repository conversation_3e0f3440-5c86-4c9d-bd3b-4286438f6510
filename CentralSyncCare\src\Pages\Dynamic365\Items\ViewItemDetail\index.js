import { Check, Clear, Close, Download, Sync, Visibility } from "@mui/icons-material";
import {
    AppBar,
    Box,
    Button,
    CircularProgress,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Grid,
    IconButton,
    Skeleton,
    styled,
    useTheme,
    Snackbar,
    RadioGroup,
    FormControlLabel,
    Radio,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    TextField,
    FormHelperText
} from "@mui/material";
import moment from "moment";
import React, { useEffect, useState } from "react";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import Typography from "@mui/material/Typography";
import PropTypes from "prop-types";
//import BasicTable from "../../../../Components/BasicTable";
import FsLightbox from "fslightbox-react";

import MuiAlert from "@mui/material/Alert";
//import ViewPolicyDialog from "../../price_policy/ViewPolicyDialog";
import parse from "html-react-parser";
import BasicTable from "../../../../Components/BasicTable";
import httpclient from "../../../../Utils";
//import BasicTable from "../../../Components/BasicTable";


const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

const StyledHeaderTitle = styled(DialogTitle)(({ theme }) => ({
    background: theme.palette.primary.main,
    color: "#fff",
    position: "relative",
    "& button": {
        position: "absolute",
        right: "15px",
        top: "15px",
        color: "#fff",
    },
}));

const FlexContent = styled("div")(({ theme }) => ({
    display: "flex",
    flexDirection: "row",
    fontSize: "17px",
    marginBottom: "10px",
    alignItems: "flex-start",
}));

const FlexInnerTitle = styled("div")(({ theme }) => ({
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    minWidth: "249px",
    maxWidth: "250px",
    fontWeight: "600",
}));

const FlexContent2 = styled("div")(({ theme }) => ({
    display: "flex",
    flexDirection: "row",
    fontSize: "17px",
}));

const FlexInnerTitle2 = styled("div")(({ theme }) => ({
    display: "flex",
    fontWeight: "600",
    gap: "5px",
    marginRight: "5px",
}));

const BoxDiv = styled("div")(({ theme }) => ({
    textAlign: "center",
}));

const Values = styled("div")(({ theme }) => ({
    marginLeft: "10px",
    fontWeight: "500",
    color: theme.palette.primary.dark,
}));

const ImageDiv = styled("div")(({ theme }) => ({
    display: "flex",
    alignItems: "center",
    width: "100%",
    flexWrap: "wrap",
    marginBottom: "10px",
}));

const ImageCell = styled("div")(({ theme }) => ({
    margin: "10px",
    width: "280px",
    borderRadius: "5px",
    overflow: "hidden",
    "& img": {
        width: "250px",
        height: "250px",
        objectFit: "cover",
        transition: "0.5s",
        boxShadow: theme.palette.primary.shadow,
        marginBottom: "10px",
        overflow: "hidden",
    },
    "& img:hover": {
        transform: "scale(1.1)",
    },
}));

const price_policyBox = styled(Box)(({ theme }) => ({
    display: "flex",
    marginBottom: "15px",
    "& h5": {
        margin: "5px",
    },
}));

const AppBarTabs = styled(AppBar)(({ theme }) => ({
    background: "#fff",
    color: theme.palette.primary.dark,
}));

function TabPanel(props) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`full-width-tabpanel-${index}`}
            aria-labelledby={`full-width-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3 }}>
                    <Typography>{children}</Typography>
                </Box>
            )}
        </div>
    );
}

TabPanel.propTypes = {
    children: PropTypes.node,
    index: PropTypes.number.isRequired,
    value: PropTypes.number.isRequired,
};

function a11yProps(index) {
    return {
        id: `full-width-tab-${index}`,
        "aria-controls": `full-width-tabpanel-${index}`,
    };
}


const saleColumns = [
    { id: "Sales_Code", name: "Sales Code" },
    { id: "Cost", name: "Cost" },
    { id: "Unit_Price", name: "Unit Price" },
    { id: "Published_Price", name: "Published Price" },
    { id: "Discount_Amount", name: "Discount Amount" },

];

const ViewItemDetail = (props) => {
    const theme = useTheme();
    const [value, setValue] = useState(0);
    const [open, setOpen] = useState(false);
    const [settingsLoading, setSettingsLoading] = useState(false);
    const [message, setMessage] = useState("");
    const [messageState, setMessageState] = useState("");
    const [togglerLanding, setTogglerLanding] = useState(false);
    const [isEdit, setIsEdit] = useState(false);
    const [imgIndex1, setImgIndex1] = useState(0);
    const [settings, setSettings] = useState([]);
    const [list, setList] = useState([]);
    const [radio, setRadio] = React.useState('0');
    const [fieldErrors, setFieldErrors] = useState({});
    const [visibleSettings, setVisibleSettings] = useState([]);

    const handleChangeRadio = (event) => {
        setRadio(event.target.value);
    };

    const handleChange = (event, newValue) => {
        setValue(newValue);
    };


    const [dialogDetails, setDialogDetails] = useState({
        open: true,
    });

    const [formData, setFormData] = useState({
        reference_id: "",
        form_value: [{}],
    });

    useEffect(() => {
        if (settings.length > 0 && (!props.viewDetails.form_values || props.viewDetails.form_values.length < 1)) {
            setFormData({
                reference_id: props.viewDetails.id || "",
                form_value: settings.map((set) => ({
                    label_name: set.name,
                    label_code: set.code,
                    label_value: set.input_type === "boolean" ? "0" : "",
                }))
            });
        }
    }, [settings, props.viewDetails]);


    useEffect(() => {
        if (props.viewDetails.form_values?.length > 0) {
            setIsEdit(true);
            setFormData({
                reference_id: props.viewDetails.id || "",
                form_value: props.viewDetails.form_values.map((set) => ({
                    label_name: set.form_label_name,
                    label_code: set.form_label_code,
                    label_value: set.setting_value || "",
                }))
            });
        }
    }, [props.viewDetails]);


    useEffect(() => {
        props.sendDetails(dialogDetails);
    }, [props, dialogDetails]);

    useEffect(() => {
        getList();
    }, []);

    const getList = () => {
        httpclient
            .get(`request-response?requestName=formsetting/setting&type=item`)
            .then(({ data }) => {
                if (data) {
                    setSettings(data.data);
                }
            })
    };

    useEffect(() => {
        settings.some((item) => {
            if (item.input_type === "list" && item.call_back_url) {
                getCallback(item);
            }
        });
    }, [settings]);

    const getCallback = (request) => {

        httpclient
            .get(`request-response?requestName=${request.call_back_url}`)
            .then(({ data }) => {
                if (data) {
                    setList(data.data);
                }
            })
    };

    const handleRefetch = () => {
        setDialogDetails({
            open: true,
            refetch: true,
            id: props.viewDetails.id,
        });
        setTimeout(() => {
            setDialogDetails({
                open: true,
                refetch: false,
                id: "",
            });
        }, 100);
    };

    // const handleSave = () => {
    //     const missingFields = settings.filter((item) => {
    //         const valueObj = formData.form_value.find((field) => field.label_code === item.code);
    //         return !valueObj || valueObj.label_value === "" || valueObj.label_value === null || valueObj.label_value === undefined;
    //     });
    //     if (missingFields.length > 0) {
    //         const errors = {};
    //         missingFields.forEach(field => {
    //             errors[field.code] = `${field.name} field is required`;
    //         });
    //         setFieldErrors(errors);
    //         return;
    //     } else {
    //         setFieldErrors({});

    //         setSettingsLoading(true);
    //         httpclient
    //             .post(`request-response?requestName=formsetting/value`, formData
    //             )
    //             .then(({ data }) => {
    //                 if (data.status === 200) {
    //                     setSettingsLoading(false);
    //                     setOpen(true);
    //                     setMessageState("success");
    //                     setMessage(data.message);
    //                     setTimeout(() => {
    //                         handleRefetch();
    //                     }, 1000);


    //                 } else {
    //                     setSettingsLoading(false);
    //                     setOpen(true);
    //                     setMessageState("error");
    //                     setMessage(data.error || data.message);
    //                 }

    //             })
    //     }
    // }

    const handleSave = () => {
        const missingFields = visibleSettings.filter((item) => {
            if (item.isRequired !== 1) return false;
    
            const valueObj = formData.form_value.find(
                (field) => field.label_code === item.code
            );
    
            return (
                !valueObj ||
                valueObj.label_value === "" ||
                valueObj.label_value === null ||
                valueObj.label_value === undefined
            );
        });
    
        if (missingFields.length > 0) {
            const errors = {};
            missingFields.forEach((field) => {
                errors[field.code] = `${field.name} field is required`;
            });
            setFieldErrors(errors);
            return;
        } else {
            setFieldErrors({});
            setSettingsLoading(true);
    
            httpclient
                .post(`request-response?requestName=formsetting/value`, formData)
                .then(({ data }) => {
                    if (data.status === 200) {
                        setSettingsLoading(false);
                        setOpen(true);
                        setMessageState("success");
                        setMessage(data.message);
                        setTimeout(() => {
                            handleRefetch();
                        }, 1000);
                    } else {
                        setSettingsLoading(false);
                        setOpen(true);
                        setMessageState("error");
                        setMessage(data.error || data.message);
                    }
                });
        }
    };
    


    const handleImageTogglerLanding = (index) => {
        setImgIndex1(index);
        setTogglerLanding((prev) => !prev);
    };

    const handleClose = () => {
        setDialogDetails({
            ...dialogDetails,
            open: false,
        });
    };

    const displayText = (descriptionTexts) => {
        const textIsEmpty = descriptionTexts === null || descriptionTexts === "";
        return !textIsEmpty ? (
            parse(descriptionTexts)
        ) : (
            "-"
        );
    }


    const handleCloseSnack = (event, reason) => {
        if (reason === "clickaway") {
            return;
        }
        setOpen(false);
    };

    const handleInputChange = (event, item) => {
        const { value } = event.target;

        setFormData((prevFormData) => ({
            ...prevFormData,
            form_value: prevFormData.form_value?.map((field) =>
                field.label_code === item.code ? { ...field, label_value: value } : field
            ) || []
        }));

        setFieldErrors(prev => ({ ...prev, [item.code]: null }));
    };


    const renderInputField = (item) => {
        switch (item.input_type) {
            case "boolean":
                return (
                    <RadioGroup
                        value={formData.form_value.find(field => field.label_code === item.code)?.label_value || "0"}
                        onChange={(event) => handleInputChange(event, item)}
                        row
                    >
                        <FormControlLabel value="1" control={<Radio />} label="Yes" />
                        <FormControlLabel value="0" control={<Radio />} label="No" />
                    </RadioGroup>
                );
            case "list":
                return (
                    <FormControl fullWidth error={!!fieldErrors[item.code]}>
                        <InputLabel>{item.name}</InputLabel>
                        <Select
                            label={item.name}
                            value={formData.form_value.find(field => field.label_code === item.code)?.label_value || ""}
                            onChange={(event) => handleInputChange(event, item)}
                        >
                            {list && list.map((l) => (
                                <MenuItem key={l.name} value={l.name}>{l.name}</MenuItem>
                            ))}
                        </Select>
                        {fieldErrors[item.code] && <FormHelperText>{fieldErrors[item.code]}</FormHelperText>}
                    </FormControl>
                );
            case "integer":
                return (
                    <TextField
                        type="number"
                        fullWidth
                        value={formData.form_value.find(field => field.label_code === item.code)?.label_value || ""}
                        onChange={(event) => handleInputChange(event, item)}
                        error={!!fieldErrors[item.code]}
                        helperText={fieldErrors[item.code] ? fieldErrors[item.code] : ""}
                    />
                );
            case "double":
                return (
                    <TextField
                        type="number"
                        inputMode="decimal"
                        fullWidth
                        value={formData.form_value.find(field => field.label_code === item.code)?.label_value || ""}
                        onChange={(event) => handleInputChange(event, item)}
                        error={!!fieldErrors[item.code]}
                        helperText={fieldErrors[item.code] ? fieldErrors[item.code] : "This field accepts decimal numbers as well."}
                    />
                );

            default:
                return (
                    <TextField
                        fullWidth
                        value={formData.form_value.find(field => field.label_code === item.code)?.label_value || ""}
                        onChange={(event) => handleInputChange(event, item)}
                        error={!!fieldErrors[item.code]}
                        helperText={fieldErrors[item.code] ? fieldErrors[item.code] : ""}
                    />
                );
        }
    };

    useEffect(() => {
        const hasOVS = formData.form_value?.some(
            (item) => item.label_code === "OVS" && item.label_value === "1"
        );

        const filtered = !hasOVS
            ? settings.filter((item) => item.code !== "TOTS")
            : settings;

        setVisibleSettings(filtered);
    }, [formData.form_value, settings]);

    return (
        <div>
            <Dialog
                open={dialogDetails.open}
                onClose={handleClose}
                maxWidth="xl"
                fullWidth
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <StyledHeaderTitle id="alert-dialog-title">
                    <div>
                        View Item Details{" "}
                        {"(" +
                            //   (props.viewDetails.handle || "-") +
                            //   "/" +
                            (props.viewDetails.description || "-") +
                            ")"}
                    </div>
                    <IconButton onClick={handleClose}>
                        <Close />
                    </IconButton>
                </StyledHeaderTitle>
                {props.singleLoading ? (
                    <DialogContent>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                                <Box p={3} sx={{ width: "100%" }}>
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                </Box>
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <Box p={3} sx={{ width: "100%" }}>
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                </Box>
                            </Grid>
                        </Grid>
                    </DialogContent>
                ) : (
                    <DialogContent sx={{ padding: "0" }}>
                        <AppBarTabs position="static">
                            <Tabs
                                value={value}
                                onChange={handleChange}
                                indicatorColor="secondary"
                                textColor="inherit"
                                variant="fullWidth"
                                aria-label="full width tabs example"
                            >

                                <Tab label="Item Details" {...a11yProps(0)} />
                                <Tab label="Price List" {...a11yProps(1)} />
                                <Tab label="Form Details" {...a11yProps(2)} />

                            </Tabs>
                        </AppBarTabs>


                        <TabPanel value={value} index={0} dir={theme.direction}>
                            <Box>
                                <Grid container>
                                    <Grid item xs={12} md={6}>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Item ID</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.id || "-"}</Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Number</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.number || "-"}</Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Description</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.description || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Base Unit of Measure</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.baseUnitOfMeasure || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Type</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.type || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Inventory Posting Group</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.inventoryPostingGroup || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Unit Price</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>${parseFloat(props.viewDetails.unitPrice).toFixed(2) || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Brand</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.dimension_values?.Name || "-"}</Values>
                                        </FlexContent>
                                    </Grid>
                                    <Grid item xs={12} md={6}>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Costing Method</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.costingMethod || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Inventory</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{parseFloat(props.viewDetails.inventory).toFixed(0) || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Purchases Quantity</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{parseFloat(props.viewDetails.purchasesQty).toFixed(0) || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Sales Quantity</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{parseFloat(props.viewDetails.salesQty).toFixed(0) || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Global Dimension Code</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.globalDimension1Code || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Last DateTime Modified</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{moment(props.viewDetails.lastDatetimeModified).format("ddd, DD MMM YYYY, h:mm A")}</Values>
                                        </FlexContent>
                                    </Grid>
                                </Grid>
                            </Box>
                        </TabPanel>
                        <TabPanel value={value} index={1} dir={theme.direction}>
                            <BasicTable
                                columns={saleColumns}
                                rows={props.viewDetails?.sale_price}
                            />
                        </TabPanel>

                        <TabPanel value={value} index={2} dir={theme.direction}>
                            <Grid container spacing={2} >
                                {/* <Grid item xs={12} md={8}>
                                    <h4>Data Fields</h4>
                                </Grid> */}

                                {visibleSettings && visibleSettings.map((item) => (
                                    <Grid container item xs={12} md={8} spacing={2} key={item.id} alignItems="center">
                                        <Grid item xs={6}>
                                            <span>{item.name}</span>
                                        </Grid>
                                        <Grid item xs={6}>{renderInputField(item)}</Grid>
                                    </Grid>
                                ))}
                            </Grid>
                            <Grid item xs={12}>
                                <Box textAlign={"right"}>
                                    {settingsLoading ?
                                        <Button
                                            variant="contained"
                                            color="primary"
                                        >
                                            <CircularProgress style={{ height: "20px", width: "20px", color: "#fff", marginRight: "10px" }} /> Loading..
                                        </Button> :
                                        <Button
                                            onClick={handleSave}
                                            color="primary"
                                            variant="contained"
                                            autoFocus
                                        >
                                            {isEdit ? `Edit` : `Save`}
                                        </Button>
                                    }
                                </Box>
                            </Grid>

                        </TabPanel>
                    </DialogContent>
                )}
                <DialogActions>
                    <Button onClick={handleClose} variant="outlined" color="primary">
                        Close
                    </Button>
                </DialogActions>
            </Dialog>



            <Snackbar
                autoHideDuration={3000}
                anchorOrigin={{ vertical: "top", horizontal: "right" }}
                open={open}
                onClose={handleCloseSnack}
            >
                <Alert
                    onClose={handleCloseSnack}
                    severity={messageState}
                    sx={{ width: "100%" }}
                >
                    {message}
                </Alert>
            </Snackbar>
        </div>
    );
};

export default ViewItemDetail;
