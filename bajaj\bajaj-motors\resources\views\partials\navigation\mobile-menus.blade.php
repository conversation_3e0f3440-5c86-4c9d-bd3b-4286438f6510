<!-- Mobile Bikes Full-Screen Menu -->
<div id="mobile-bikes-dropdown" class="lg:hidden">
    <!-- Header -->
    <div class="flex items-center justify-between p-4 border-b border-gray-200">
        <span class="text-lg font-semibold text-gray-900">MOTORCYCLES</span>
        <button id="mobile-bikes-close" class="w-8 h-8 flex items-center justify-center">
            <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    </div>

    <!-- Content -->
    <div class="flex-1 overflow-y-auto">
        <div id="mobile-bikes-main" class="p-4">
            <div id="mobile-bikes-brands" class="space-y-1">
                <!-- Brands will be populated by JavaScript or server-side -->
                @if(isset($bikeBrands))
                    @foreach($bikeBrands as $brandName => $bikes)
                        <button class="mobile-brand-item w-full text-left px-4 py-3 text-lg font-medium text-gray-900 hover:bg-gray-50 rounded-lg transition-colors" data-brand="{{ $brandName }}">
                            {{ $brandName }}
                        </button>
                    @endforeach
                @endif
            </div>
        </div>

        <!-- Brand Detail View -->
        <div id="mobile-bikes-brand-detail" class="hidden">
            <!-- Brand Detail Header -->
            <div class="flex items-center p-4 border-b border-gray-200">
                <button id="mobile-bikes-back-btn" class="w-8 h-8 flex items-center justify-center mr-3">
                    <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>
                <span id="mobile-bikes-brand-title" class="text-lg font-semibold text-gray-900">BIKES/PULSAR</span>
                <button id="mobile-bikes-detail-close" class="w-8 h-8 flex items-center justify-center ml-auto">
                    <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Category Tabs -->
            <div class="border-b border-gray-200">
                <div id="mobile-bikes-category-tabs" class="flex overflow-x-auto px-4 py-2">
                    <!-- Category tabs will be populated by JavaScript -->
                </div>
            </div>

            <!-- Motorcycle List -->
            <div id="mobile-bikes-motorcycle-list" class="p-4">
                <!-- Motorcycles will be populated by JavaScript -->
            </div>
        </div>
    </div>
</div>

<!-- Mobile Menu Overlay -->
<div id="mobile-menu-overlay" class="fixed inset-0 bg-white z-50 transform translate-x-full transition-transform duration-300 lg:hidden">
    <!-- Mobile Menu Header -->
    <div class="flex items-center justify-between p-4 border-b border-gray-200">
        <span class="text-lg font-semibold text-gray-900">Menu</span>
        <button id="mobile-menu-close" class="w-8 h-8 flex items-center justify-center">
            <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    </div>

    <!-- Mobile Menu Content -->
    <div id="mobile-menu-content" class="flex-1 overflow-y-auto">
        <!-- Main Menu Items -->
        <div id="mobile-main-menu" class="p-4">
            <div class="space-y-1">
                <a href="{{ route('home') }}" class="mobile-menu-item block px-4 py-3 text-lg font-medium text-gray-900 hover:bg-gray-50 rounded-lg transition-colors">HOME</a>
                <a href="#" class="mobile-menu-item block px-4 py-3 text-lg font-medium text-gray-900 hover:bg-gray-50 rounded-lg transition-colors">SHOWROOMS</a>
                <a href="#" class="mobile-menu-item block px-4 py-3 text-lg font-medium text-gray-900 hover:bg-gray-50 rounded-lg transition-colors">WORKSHOPS</a>
                <a href="#" class="mobile-menu-item block px-4 py-3 text-lg font-medium text-gray-900 hover:bg-gray-50 rounded-lg transition-colors">EVENTS</a>
                <a href="#" class="mobile-menu-item block px-4 py-3 text-lg font-medium text-gray-900 hover:bg-gray-50 rounded-lg transition-colors">BOOK TEST RIDE</a>
                <a href="{{ route('about') }}" class="mobile-menu-item block px-4 py-3 text-lg font-medium text-gray-900 hover:bg-gray-50 rounded-lg transition-colors">ABOUT US</a>
                <a href="#" class="mobile-menu-item block px-4 py-3 text-lg font-medium text-gray-900 hover:bg-gray-50 rounded-lg transition-colors">NEWS</a>
                <button class="mobile-menu-item w-full text-left px-4 py-3 text-lg font-medium text-gray-900 hover:bg-gray-50 rounded-lg transition-colors flex items-center justify-between" id="mobile-media-btn">
                    <span>MEDIA CENTER</span>
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Media Center Submenu -->
        <div id="mobile-media-menu" class="hidden">
            <div class="p-4">
                <div class="flex items-center mb-4">
                    <button id="mobile-media-back" class="w-8 h-8 flex items-center justify-center mr-3">
                        <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </button>
                    <h3 class="text-lg font-semibold text-gray-900">MEDIA CENTER</h3>
                </div>
                <div class="space-y-1">
                    <a href="{{ route('about') }}" class="mobile-menu-item block px-4 py-3 text-base text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">ABOUT US</a>
                    <a href="#" class="mobile-menu-item block px-4 py-3 text-base text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">ANNOUNCEMENTS</a>
                    <a href="#" class="mobile-menu-item block px-4 py-3 text-base text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">EVENTS</a>
                    <a href="{{ route('blogs.index') }}" class="mobile-menu-item block px-4 py-3 text-base text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">BLOGS</a>
                    <a href="#" class="mobile-menu-item block px-4 py-3 text-base text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">DOWNLOAD CENTER</a>
                    <a href="#" class="mobile-menu-item block px-4 py-3 text-base text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">CONTACT US</a>
                    <a href="#" class="mobile-menu-item block px-4 py-3 text-base text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">FAQS</a>
                </div>
            </div>
        </div>
    </div>
</div>
