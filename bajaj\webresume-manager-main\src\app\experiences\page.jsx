"use client";
import { deleteExperience } from "@/actions/experience";
import { Button } from "@/components/ui/button";
import { FilePenLine, Trash2 } from "lucide-react";
import { useSession } from "next-auth/react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";

const page = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [experiences, setExperiences] = useState([]);

  //check authentication
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/");
    }
  }, [status, router]);

  //fetch experience
  useEffect(() => {
    const fetchExperiences = async () => {
      if (!session?.user?.userId) return;

      try {
        const res = await fetch(`/api/experience/${session.user.userId}`);
        const data = await res.json();
        setExperiences(data);
      } catch (err) {
        console.error("Error fetching experiences:", err);
      }
    };

    fetchExperiences();
  }, [session, status]);

  //handle delete experience
  const handleDelete = async (experienceId) => {
    try {
      await deleteExperience(experienceId);
      setExperiences((prevExperiences) =>
        prevExperiences.filter((exp) => exp._id !== experienceId)
      );
      toast.success("Experience deleted successfully!");
    } catch (error) {
      console.log("Error in handle delete", error);
    }
  };

  //handle edit experience
  const handleEdit = (experienceId) => {
    router.push(`/experiences/editexperience/?id=${experienceId}`);
  };

  if (status === "loading") {
    return <p>Loading...</p>;
  }

  return (
    <div className="px-4 md:px-8 xl:px-16">
      <div className="flex items-center gap-16 mb-12">
        <h1 className="text-3xl font-semibold">Experience Management</h1>
        <Link href={"/experiences/addexperience"}>
          <button className="bg-gray-900 rounded-xl text-white px-4 py-2 hover:bg-gray-800 cursor-pointer">
            Add Experience
          </button>
        </Link>
      </div>

      <div className="flex gap-6 flex-wrap">
        {experiences.length ? (
          experiences.map((experience) => (
            <div
              key={experience._id}
              className="bg-gray-100 border shadow-lg w-72 rounded-xl"
            >
              <Image
                src={experience.image || "/no-image.png"}
                width={180}
                height={120}
                alt="experience image"
                className="rounded-t-xl h-30 w-full object-cover"
              />
              <div>
                <h2 className="text-xl font-semibold pt-3 pb-1 px-2">
                  Worked as {experience.role}
                </h2>
                <p className="text-sm px-2 text-gray-600">
                  at {experience.company}
                </p>

                <p className="text-sm px-2 pt-2 text-gray-800">
                  {experience.desc}
                </p>

                <div className="flex items-center justify-between mt-2">
                  <div className="flex flex-col gap-1 py-1 mb-4 justify-around text-gray-600 bg-gray-100 rounded-lg">
                    <div className="px-4 text-xs text-start">
                      From{" "}
                      {new Date(experience.joiningDate)
                        .toISOString()
                        .slice(0, 10)}
                    </div>

                    {experience.resignDate === "working" ? (
                      <p className="px-4 text-xs text-start">Ongoing</p>
                    ) : (
                      <div className="px-4 text-xs text-start">
                        To{" "}
                        {new Date(experience.resignDate)
                          .toISOString()
                          .slice(0, 10)}
                      </div>
                    )}
                  </div>

                  <div className="flex items-center justify-end gap-2 px-2  pb-4">
                    <Button
                      onClick={() => handleEdit(experience._id)}
                      className="bg-gray-200 hover:bg-gray-300 cursor-pointer"
                    >
                      <FilePenLine />
                    </Button>
                    <Button
                      variant="destructive"
                      onClick={() => handleDelete(experience._id)}
                      className="bg-gray-200 hover:bg-gray-300 cursor-pointer"
                    >
                      <Trash2 className="text-red-500" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ))
        ) : (
          <p>No Experience Found....</p>
        )}
      </div>
    </div>
  );
};

export default page;
