<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Bike extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'brand_name',
        'category',
        'description',
        'featured',
        'hero_image',
        'price_starting',
        'engine_capacity',
        'mileage',
        'top_speed',
        'meta_title',
        'meta_description'
    ];

    protected $casts = [
        'featured' => 'boolean',
        'price_starting' => 'decimal:2'
    ];

    /**
     * Get the bike colors
     */
    public function colors()
    {
        return $this->hasMany(BikeColor::class);
    }

    /**
     * Get the bike variants
     */
    public function variants()
    {
        return $this->hasMany(BikeVariant::class);
    }

    /**
     * Get the bike specifications
     */
    public function specifications()
    {
        return $this->hasMany(BikeSpecification::class);
    }

    /**
     * Get the bike images
     */
    public function images()
    {
        return $this->hasMany(BikeImage::class);
    }

    /**
     * Scope for featured bikes
     */
    public function scopeFeatured($query)
    {
        return $query->where('featured', true);
    }

    /**
     * Scope for bikes by brand
     */
    public function scopeByBrand($query, $brand)
    {
        return $query->where('brand_name', $brand);
    }

    /**
     * Scope for bikes by category
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Get the brand logo path
     */
    public function getBrandLogoAttribute()
    {
        return asset("assets/brand-logos/" . strtolower($this->brand_name) . "-logo.png");
    }

    /**
     * Get the category icon path
     */
    public function getCategoryIconAttribute()
    {
        return asset("assets/category-icons/" . strtolower($this->brand_name) . "-category-icon.png");
    }
}
