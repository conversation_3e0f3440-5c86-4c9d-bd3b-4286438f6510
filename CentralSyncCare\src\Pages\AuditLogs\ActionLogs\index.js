import React, { useEffect, useState, useRef } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Check,
  Clear,
  Close,
  Download,
  FilterList,
} from "@mui/icons-material";
import TableComponent from "../TableComponent";
import httpclient from "../../../Utils";
import {
  Box,
  Button,
  Card,
  Collapse,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  styled,
  TextField,
  Snackbar,
  Autocomplete,
} from "@mui/material";
// import ViewOrderDialog from "../ViewOrderDialog";
import MuiAlert from "@mui/material/Alert";
// import StatusDialog from "../StatusDialog";
// import BackdropLoader from "../../../../Components/BackdropLoader";
import { useLocation, useNavigate } from "react-router";
import ViewActionLogDetail from "./ViewActionLogDetail";
import useTokenRefresh from "../../../Hooks/useTokenRefresh";
import Footer from "../../../Components/Footer";


//import { useLocation } from "react-router-dom";

const Alert = React.forwardRef(function Alert(props, ref) {
  return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});



const columns = [
  //{ id: "checkColumn", name: " " },
  { id: "sn", name: "SN" },
  { id: "user_name", name: "Action By" },
  { id: "module_name", name: "Module Name" },
  { id: "ip_address", name: "IP Address" },
  { id: "user_agent", name: "Action Agent" },
  { id: "method", name: "Method" },
  { id: "action", name: "Action" },
  { id: "action_date", name: "Action Date" },
];

const FilteredBox = styled(Box)(({ theme }) => ({
  background: "#f9f9f9",
  padding: "5px 10px",
  borderRadius: "5px",
  "& p": {
    margin: "3px 0",
    marginRight: "10px",
    display: "inline-block",
    background: "#dedede",
    borderRadius: "10px",
    padding: "2px 5px",
  },
  "& svg": {
    fontSize: "15px",
    cursor: "pointer",
    position: "relative",
    top: "3px",
    background: theme.palette.primary.dark,
    color: "#fff",
    borderRadius: "50%",
    padding: "2px",
    marginLeft: "2px",
  },
}));

const Header = styled("div")(({ theme }) => ({
  "& h1": {
    color: theme.palette.primary.dark,
    margin: "0",
  },
}));

const configRowPerPage = JSON.parse(localStorage.getItem("configRowPerPage"));

const ActionLogs = (props) => {

  const { getTokenRefreshed: refresh, open: tokenOpen, overlay: overlay, setOpen: setTokenOpen, message: tokenMessage, messageState: tokenMessageState } = useTokenRefresh();

  const location = useLocation();
  const navigate = useNavigate();
  const buttonRef = useRef(null);

  const [openViewDialog, setOpenViewDialog] = useState(false);
  const [viewDetails, setViewDetails] = useState({});

  const [rows, setRows] = useState([]);
  const [rowChecked, setRowChecked] = useState([]);
  const [methods, setMethods] = useState([]);
  const [actions, setActions] = useState([]);
  const [buttonLoader, setButtonLoader] = useState(false);
  const [backdropLoader, setBackdropLoader] = useState(false);
  const [loading, setLoading] = useState(false);
  const [singleLoading, setSingleLoading] = useState(false);
  const [direction, setDirection] = useState(false);
  const [currentColumn, setCurrentColumn] = useState("");
  const [page, setPage] = useState(1);
  const [from, setFrom] = useState(1);
  const [to, setTo] = useState(
    configRowPerPage && configRowPerPage
      ? configRowPerPage && configRowPerPage
      : 20
  );

  const [rowsPerPage, setRowsPerPage] = useState(
    configRowPerPage && configRowPerPage
      ? configRowPerPage && configRowPerPage
      : 20
  );
  const [total, setTotal] = useState("");
  const [filterOpen, setFilterOpen] = useState(false);

  const [open, setOpen] = useState(false);
  const [message, setMessage] = useState("");
  const [messageState, setMessageState] = useState("");
  const [modules, setModules] = useState([]);
  const [filterData, setFilterData] = useState({

    module_name: "",
    method: "",
    module_id: "",
    action: "",
    startDate: "",
    endDate: "",
    remove: false,
  });

  const [submittedData, setSubmittedData] = useState({

    module_name: "",
    method: "",
    module_id: "",
    action: "",
    startDate: "",
    endDate: "",

    submit: false,
  });

  useEffect(() => {
    if (

      filterData.module_name === "" ||
      filterData.method === "" ||
      filterData.module_id === "" ||
      filterData.action === "" ||
      filterData.startDate === "" ||
      filterData.endDate === ""

    ) {
      setSubmittedData({
        ...submittedData,
        submit: false,
      });
    }

    if (filterData.module_name === " ") filterData.module_name = "";
    if (filterData.method === " ") filterData.method = "";
    if (filterData.module_id === " ") filterData.module_id = "";
    if (filterData.action === " ") filterData.action = "";
    if (filterData.startDate === " ") filterData.startDate = "";
    if (filterData.endDate === " ") filterData.endDate = "";

    filterData.remove === true && handleFilter();
  }, [filterData]);

  useEffect(() => {
    let currentpolicy = JSON.parse(localStorage.getItem("audit_action_logs"));
    currentpolicy !== null && setFilterData(currentpolicy);

    currentpolicy == null
      ? getActionLogs()
      :
      currentpolicy.module_name == "" &&
        currentpolicy.method == "" &&
        currentpolicy.module_id == "" &&
        currentpolicy.action == "" &&
        currentpolicy.startDate == "" &&
        currentpolicy.endDate == "" &&

        currentpolicy.removed == false
        ? getActionLogs()
        : console.log("actions");
  }, []);


  const getActionLogs = () => {
    setLoading(true);
    httpclient
      .get(`audit/action-logs?pagination=${rowsPerPage}&page=${page}`)
      .then(({ data }) => {
        if (data.status === 200) {
          setRows(data.data);
          setModules(data.moduleList);
          setMethods(data.methodList);
          setActions(data.actionList);
          setTotal(data.meta.total);
          setRowsPerPage(parseInt(data.meta.per_page));
          setPage(data.meta.current_page);
          setFrom(data.meta.from);
          setTo(data.meta.to);
          setLoading(false);
        } else {
          setOpen(true);
          setMessage(data.message);
          setMessageState("error");
          setLoading(false);
        }

      }).catch((err) => {
        if (err.response.status === 401) {
          refresh();
          setOpen(tokenOpen);
          setMessage(tokenMessage);
          setMessageState("error");
        } else if (err.response.status === 422) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setLoading(false);
        } else if (err.response.status === 400) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setLoading(false);

        } else {
          setOpen(true);
          setMessage(err.response.data.message);
          setMessageState("error");
          setLoading(false);
        }
      })
  };


  // const handleView = (row) => {
  //   setSingleLoading(true);
  //   setOpenViewDialog(true);
  //   httpclient
  //     .get(`audit/action-logs/${row.id || row}`)
  //     .then(({ data }) => {
  //       if (data) {
  //         setViewDetails(data.data);
  //         setSingleLoading(false);
  //       }
  //       else {
  //         setOpen(true);
  //         setMessage(data.message);
  //         setMessageState("error");
  //         setLoading(false);
  //       }

  //     }).catch((err) => {
  //       if (err.response.status === 401) {
  //         refresh();
  //         setOpen(tokenOpen);
  //         setMessage(tokenMessage);
  //         setMessageState("error");
  //       } else if (err.response.status === 422) {
  //         const errorMessages = Object.values(err.response.data.errors).flat();
  //         setOpen(true);
  //         setMessage(errorMessages);
  //         setMessageState("error");
  //         setLoading(false);
  //       } else if (err.response.status === 400) {
  //         const errorMessages = Object.values(err.response.data.errors).flat();
  //         setOpen(true);
  //         setMessage(errorMessages);
  //         setMessageState("error");
  //         setLoading(false);

  //       } else {
  //         setOpen(true);
  //         setMessage(err.response.data.message);
  //         setMessageState("error");
  //         setLoading(false);
  //       }
  //     })
  // };

  const handleView = (row) => {
    setSingleLoading(true);
    setOpenViewDialog(true);
    setTimeout(() => {
      setViewDetails(row);
      setSingleLoading(false);
    }, 1000);
  };

  const sendDetails = (callback) => {
    if (callback.open === false) {
      setOpenViewDialog(false);
      setViewDetails({});
    }
    if (callback.refetch === true) {
      handleView(callback.id);
      setTimeout(() => {
        handleFilter();
      }, 1000);
    }
  };

  const handleFilter = () => {
    setSubmittedData({
      ...submittedData,

      module_name: filterData.module_name,
      method: filterData.method,
      module_id: filterData.module_id,
      action: filterData.action,
      startDate: filterData.startDate,
      endDate: filterData.endDate,
      submit: true,
    });

    filterData.remove = true;

    localStorage.setItem("audit_action_logs", JSON.stringify(filterData));


    setLoading(true);
    if (
      filterData.module_name ||
      filterData.method ||
      filterData.module_id ||
      filterData.action ||
      filterData.startDate ||
      filterData.endDate
    ) {

      httpclient
        .get(
          `audit/action-logs?filters[method][$eq]=${filterData.method
          }&filters[module_id][$eq]=${filterData.module_id
          }&filters[action][$eq]=${filterData.action
          }&filters[action_date][$between][0]=${filterData.startDate
          }&filters[action_date][$between][1]=${filterData.endDate
          }&pagination=${rowsPerPage}&page=${1}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setModules(data.moduleList);
            setMethods(data.methodList);
            setActions(data.actionList);
            setTotal(data.meta.total);
            setRowsPerPage(data.meta.per_page);
            setPage(data.meta.current_page);
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }
        });
    } else {
      getActionLogs();
    }
  };



  const hadleFilterOpen = () => {
    setFilterOpen((prev) => !prev);
  };

  const handleChangeModules = (value) => {
    setFilterData({
      ...filterData,
      module_id: value !== null ? value.id : "",
      module_name: value !== null ? value.name : "",
      remove: false,
    });
  };

  const handleChangeFilter = (e) => {
    const { name, value } = e.target;
    setFilterData({
      ...filterData,
      [name]: value,
      remove: false,
    });
  };

  const handleRemove = (data) => {

    if (data === "module_id") {
      filterData.module_name = "";
      submittedData.module_name = "";
    }
    if (data === "startDate") {
      setFilterData({
        ...filterData,
        startDate: "",
        endDate: "",
        remove: true,
      });
      setSubmittedData({
        ...submittedData,
        startDate: "",
        endDate: "",
      });
    } else {
      setFilterData({
        ...filterData,
        [data]: "",
        remove: true,
      });

      setSubmittedData({
        ...submittedData,
        [data]: "",
      });
    }
  };


  const handleSort = (column) => {
    setDirection((prevDirection) => !prevDirection);
    setCurrentColumn(column);
    setLoading(true);
    submittedData.submit
      ? httpclient
        .get(
          `audit/action-logs?filters[method][$eq]=${filterData.method
          }&filters[module_id][$eq]=${filterData.module_id
          }&filters[action][$eq]=${filterData.action
          }&filters[action_date][$between][0]=${filterData.startDate
          }&filters[action_date][$between][1]=${filterData.endDate
          }&sort[0]=${column}:${!direction ? "asc" : "desc"
          }&pagination=${rowsPerPage}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setTotal(data.meta.total);
            setRowsPerPage(parseInt(data.meta.per_page));
            setPage(data.meta.current_page);
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }
        })

      : httpclient
        .get(
          `audit/action-logs?sort[0]=${column}:${!direction ? "asc" : "desc"
          }&pagination=${rowsPerPage}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }

        });
  };

  const handleChangePage = (e, page) => {
    setLoading(true);
    submittedData.submit
      ? httpclient
        .get(
          `audit/action-logs?filters[method][$eq]=${filterData.method
          }&filters[module_id][$eq]=${filterData.module_id
          }&filters[action][$eq]=${filterData.action
          }&filters[action_date][$between][0]=${filterData.startDate
          }&filters[action_date][$between][1]=${filterData.endDate
          }${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''
          }&pagination=${rowsPerPage}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setTotal(data.meta.total);
            setRowsPerPage(parseInt(data.meta.per_page));
            setPage(data.meta.current_page);
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }

        })
      : httpclient
        .get(
          `audit/action-logs?${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''}&pagination=${rowsPerPage}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setTotal(data.meta.total);
            setRowsPerPage(parseInt(data.meta.per_page));
            setPage(data.meta.current_page);
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }

        });
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(+event.target.value);
    setLoading(true);

    localStorage.setItem("configRowPerPage", event.target.value);

    submittedData.submit
      ? httpclient
        .get(
          `audit/action-logs?filters[method][$eq]=${filterData.method
          }&filters[module_id][$eq]=${filterData.module_id
          }&filters[action][$eq]=${filterData.action
          }&filters[action_date][$between][0]=${filterData.startDate
          }&filters[action_date][$between][1]=${filterData.endDate
          }${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''
          }&pagination=${+event.target.value}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setTotal(data.meta.total);
            setRowsPerPage(parseInt(data.meta.per_page));
            setPage(data.meta.current_page);
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }
        })
      : httpclient
        .get(
          `audit/action-logs?${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''}&pagination=${+event.target.value}&page=${1}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setTotal(data.meta.total);
            setRowsPerPage(parseInt(data.meta.per_page));
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setPage(data.meta.current_page);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }
        })
  };

  const handleClose = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }
    setOpen(false);
    setTokenOpen(false);
  };


  return (
    <div>
      <Grid container spacing={2}>
        <Grid item md={8} xs={12}>
          <Header>
            <h1>List Action Logs</h1>
          </Header>
        </Grid>
        <Grid
          item
          md={4}
          xs={12}
          display="flex"
          alignItems="center"
          justifyContent="flex-end"
        >
          <Button color="primary" variant="contained" onClick={hadleFilterOpen}>
            Filter <FilterList style={{ marginLeft: "5px" }} fontSize="small" />
          </Button>
        </Grid>

        {/* Filter */}
        <Grid item xs={12}>
          <Collapse in={filterOpen}>
            <Card>
              <Box p={4}>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={4}>
                    <InputLabel>Module</InputLabel>
                    <Autocomplete
                      disablePortal
                      id="module_id"
                      options={modules}
                      onChange={(event, newValue) => {
                        handleChangeModules(newValue);
                      }}
                      inputValue={filterData.module_name}
                      getOptionLabel={(option) => option.name}
                      renderOption={(props, option, index) => { return <li {...props} key={option.module_name}>{option.name}</li> }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          onChange={handleChangeFilter}
                          onKeyDown={(e) => {
                            if (e.key === "Enter") handleFilter();
                          }}
                          value={filterData.module_name}
                          variant="outlined"
                          name="module_id"

                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <InputLabel>Method</InputLabel>
                    <FormControl fullWidth>
                      <Select
                        name="method"
                        value={filterData.method}
                        onChange={handleChangeFilter}
                        onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                      >
                        <MenuItem value={""}>Select</MenuItem>
                        {methods && methods.map((m) => (
                          <MenuItem value={m.name}>{m.name}</MenuItem>
                        ))}

                      </Select>
                    </FormControl>


                  </Grid>

                  <Grid item xs={12} md={4}>
                    <InputLabel>Action</InputLabel>
                    <FormControl fullWidth>
                      <Select
                        name="action"
                        value={filterData.action}
                        onChange={handleChangeFilter}
                        onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                      >
                        <MenuItem value={""}>Select</MenuItem>
                        {actions && actions.map((a) => (
                          <MenuItem value={a.name}>{a.name}</MenuItem>
                        ))}

                      </Select>
                    </FormControl>


                  </Grid>
                  <Grid item xs={12} md={4}>
                    <InputLabel>Start Date</InputLabel>
                    <TextField
                      variant="outlined"
                      name="startDate"
                      type="date"
                      value={filterData.startDate}
                      onChange={(e) => handleChangeFilter(e)}
                      fullWidth
                      InputLabelProps={{
                        shrink: true,
                      }}
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <InputLabel>End Date</InputLabel>
                    <TextField
                      variant="outlined"
                      name="endDate"
                      type="date"
                      value={filterData.endDate}
                      onChange={(e) => handleChangeFilter(e)}
                      fullWidth
                      InputLabelProps={{
                        shrink: true,
                      }}
                    />
                  </Grid>


                  <Grid item xs={12}>
                    <Box textAlign={"right"}>
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={handleFilter}
                      >
                        Filter{" "}
                        <ArrowForward
                          fontSize="small"
                          style={{ marginLeft: "5px" }}
                        />
                      </Button>
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            </Card>
          </Collapse>
        </Grid>

        {
          submittedData.method ||
            submittedData.module_id ||
            submittedData.action ||
            submittedData.startDate ||
            submittedData.endDate
            ? (
              <Grid item xs={12}>
                <FilteredBox>
                  <span>Filtered: </span>


                  {submittedData.module_name && (
                    <p>
                      <span>Module: {submittedData.module_name}</span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("module_id")}
                      />
                    </p>
                  )}
                  {submittedData.method && (
                    <p>
                      <span>Method: {submittedData.method}</span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("method")}
                      />
                    </p>
                  )}
                  {submittedData.action && (
                    <p>
                      <span>Action: {submittedData.action}</span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("action")}
                      />
                    </p>
                  )}
                  {(submittedData.startDate || submittedData.endDate) && (
                    <p>
                      <span>
                        Action Date Range: {submittedData.startDate} -{" "}
                        {submittedData.endDate}
                      </span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("startDate")}
                      />
                    </p>
                  )}
                </FilteredBox>
              </Grid>
            ) : (
              <Box></Box>
            )}
        {/* Filter */}



        <Grid item xs={12}>
          <TableComponent
            columns={columns}
            rows={rows}
            sort={true}
            handleView={handleView}
            handleSort={handleSort}
            loading={loading}
            handleChangeRowsPerPage={handleChangeRowsPerPage}
            handleChangePage={handleChangePage}
            rowChecked={rowChecked}
            buttonLoader={buttonLoader}
            direction={direction}
            currentColumn={currentColumn}
            page={page}
            total={total && total}
            fromTable={from}
            toTable={to}
            rowsPerPage={rowsPerPage}
            filterData={filterData}
          />
        </Grid>
        <Footer overlay={overlay || props.overlayNew} />
      </Grid>

      {openViewDialog && (
        <ViewActionLogDetail
          singleLoading={singleLoading}
          viewDetails={viewDetails}
          sendDetails={sendDetails}
        />
      )}

      <Snackbar
        autoHideDuration={3000}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
        open={open || tokenOpen}
        onClose={handleClose}
      >
        <Alert
          onClose={handleClose}
          severity={messageState || tokenMessageState}
          sx={{ width: "100%" }}
        >
          {message || tokenMessage}
        </Alert>
      </Snackbar>
    </div>
  );
};

export default ActionLogs;