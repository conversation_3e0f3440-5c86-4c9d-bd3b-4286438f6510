// Main Application Module
import { CarouselManager } from "./carousel.js";
import { NavbarManager } from "./navbar.js";

class BajajApp {
  constructor() {
    this.carouselManager = null;
    this.navbarManager = null;
  }

  // Initialize the application
  async initialize() {
    // Initialize carousel
    this.carouselManager = new CarouselManager();
    await this.carouselManager.initialize();

    // Initialize navbar (loads data when needed)
    this.navbarManager = new NavbarManager();
    this.navbarManager.initialize();

    // Make navbar manager globally available for HTML onclick handlers
    window.navbarManager = this.navbarManager;
  }

}

// Initialize the application when DOM is loaded
document.addEventListener("DOMContentLoaded", async function () {
  const app = new BajajApp();
  await app.initialize();
});

// Export for potential module usage
export default BajajApp;

// Hero Carousel Functionality
document.addEventListener('DOMContentLoaded', () => {
  // Hero Carousel
  const heroCarousel = {
    slides: document.querySelectorAll('.carousel-slide'),
    indicators: document.querySelectorAll('.carousel-indicators .indicator'),
    prevBtn: document.querySelector('.carousel-control.prev'),
    nextBtn: document.querySelector('.carousel-control.next'),
    currentSlide: 0,
    interval: null,

    init() {
      this.prevBtn.addEventListener('click', () => this.prevSlide());
      this.nextBtn.addEventListener('click', () => this.nextSlide());
      this.indicators.forEach((indicator, index) => {
        indicator.addEventListener('click', () => this.goToSlide(index));
      });
      this.startAutoPlay();
    },

    showSlide(index) {
      this.slides.forEach(slide => slide.classList.remove('active'));
      this.indicators.forEach(indicator => indicator.classList.remove('active'));
      
      this.slides[index].classList.add('active');
      this.indicators[index].classList.add('active');
      this.currentSlide = index;
    },

    nextSlide() {
      const next = (this.currentSlide + 1) % this.slides.length;
      this.showSlide(next);
    },

    prevSlide() {
      const prev = (this.currentSlide - 1 + this.slides.length) % this.slides.length;
      this.showSlide(prev);
    },

    goToSlide(index) {
      this.showSlide(index);
    },

    startAutoPlay() {
      this.interval = setInterval(() => this.nextSlide(), 5000);
    },

    stopAutoPlay() {
      clearInterval(this.interval);
    }
  };

  // Bike Carousel Functionality
  const bikeCarousel = {
    brandSections: document.querySelectorAll('.bike-brand-section'),
    brandButtons: document.querySelectorAll('.brand-btn'),
    prevBtn: document.querySelector('.bike-carousel-controls .prev-btn'),
    nextBtn: document.querySelector('.bike-carousel-controls .next-btn'),
    currentBrand: 0,

    init() {
      this.brandButtons.forEach((btn, index) => {
        btn.addEventListener('click', () => this.switchBrand(index));
      });
      this.prevBtn.addEventListener('click', () => this.prevBrand());
      this.nextBtn.addEventListener('click', () => this.nextBrand());
    },

    switchBrand(index) {
      this.brandButtons.forEach(btn => btn.classList.remove('active'));
      this.brandSections.forEach(section => section.classList.remove('active'));
      
      this.brandButtons[index].classList.add('active');
      this.brandSections[index].classList.add('active');
      this.currentBrand = index;
    },

    nextBrand() {
      const next = (this.currentBrand + 1) % this.brandSections.length;
      this.switchBrand(next);
    },

    prevBrand() {
      const prev = (this.currentBrand - 1 + this.brandSections.length) % this.brandSections.length;
      this.switchBrand(prev);
    }
  };

  // Color Switcher for Bikes
  const colorSwitcher = {
    init() {
      document.querySelectorAll('.bike-card').forEach(card => {
        const colorBtns = card.querySelectorAll('.color-btn');
        const bikeImage = card.querySelector('.bike-image img');
        
        colorBtns.forEach(btn => {
          btn.addEventListener('click', () => {
            const color = btn.dataset.color;
            const bikeId = card.dataset.id;
            
            // Update active state
            colorBtns.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            
            // Update image based on color
            const newImagePath = `/assets/bikes/${bikeId.split('-')[0]}/${bikeId}-${color}.svg`;
            bikeImage.src = newImagePath;
          });
        });
      });
    }
  };

  // Initialize all components
  heroCarousel.init();
  bikeCarousel.init();
  colorSwitcher.init();
});
