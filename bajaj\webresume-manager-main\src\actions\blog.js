"use server";

import { connectToDB } from "@/lib/mongodb";
import Blog from "@/models/blog";

export const deleteBlog = async (blogId) => {
  await connectToDB();

  try {
    await Blog.findByIdAndDelete(blogId);
    return;
  } catch (error) {
    console.log("Error in deleteblog action", error);
  }
};

// export const updateBlog = async (blogId, formData) => {
//   await connectToDB();

//   try {
//     await Blog.findByIdAndUpdate(
//       blogId,
//       { title: formData.title, desc: formData.desc },
//       { new: true }
//     );
//     return;
//   } catch (error) {
//     console.log("Error in update blog", error);
//   }
// };

export const getBlogById = async (blogId) => {
  await connectToDB();

  try {
    //finding existing blog
    const blog = await Blog.findById(blogId);
    const data = {
      title: blog.title,
      category: blog.category,
      desc: blog.desc,
      image: blog.image,
    };

    return data;
  } catch (error) {
    console.log("Error in get blog by id", error);
  }
};
