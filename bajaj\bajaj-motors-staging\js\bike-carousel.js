// Simple Bike Carousel - No Data Arrays
document.addEventListener('DOMContentLoaded', () => {
  // Brand tab switching
  const brandTabs = document.querySelectorAll('.brand-tab');
  const brandLogo = document.querySelector('.brand-logo');
  const categoryIcon = document.querySelector('.category-icon');
  const categoryText = document.querySelector('.category-display-text');
  const backgroundText = document.querySelector('.background-brand-text');
  const modelGroups = document.querySelectorAll('.model-group');

  brandTabs.forEach(tab => {
    tab.addEventListener('click', () => {
      // Remove active class from all tabs
      brandTabs.forEach(t => {
        t.classList.remove('active', 'text-gray-800', 'border-black');
        t.classList.add('text-gray-400', 'border-transparent');
      });

      // Add active class to clicked tab
      tab.classList.add('active', 'text-gray-800', 'border-black');
      tab.classList.remove('text-gray-400', 'border-transparent');

      // Update brand logo and category
      if (brandLogo) brandLogo.src = tab.dataset.logo;
      if (categoryIcon) categoryIcon.src = tab.dataset.categoryIcon;
      if (categoryText) categoryText.textContent = tab.dataset.category;
      if (backgroundText) backgroundText.textContent = tab.dataset.brand;

      // Show/hide model groups
      const brand = tab.dataset.brand.toLowerCase();
      modelGroups.forEach(group => {
        if (group.classList.contains(`${brand}-models`)) {
          group.classList.remove('hidden');
          // Set first model as active
          const firstModel = group.querySelector('.variant-btn');
          if (firstModel) {
            updateModelDetails(firstModel);
            setActiveModel(firstModel);
          }
        } else {
          group.classList.add('hidden');
        }
      });
    });
  });

  // Color button switching
  const colorBtns = document.querySelectorAll('.color-btn');
  colorBtns.forEach(btn => {
    btn.addEventListener('click', () => {
      colorBtns.forEach(b => b.classList.remove('active'));
      btn.classList.add('active');
    });
  });

  // Model switching
  const modelBtns = document.querySelectorAll('.variant-btn');
  const bikeImage = document.querySelector('.bike-image');
  const bikeTitle = document.querySelector('.bike-title');
  const bikeDescription = document.querySelector('.bike-description');

  function setActiveModel(activeBtn) {
    modelBtns.forEach(btn => {
      btn.classList.remove('active', 'text-gray-700', 'border-black');
      btn.classList.add('text-gray-500', 'border-transparent');
    });
    activeBtn.classList.add('active', 'text-gray-700', 'border-black');
    activeBtn.classList.remove('text-gray-500', 'border-transparent');
  }

  function updateModelDetails(btn) {
    if (bikeImage) bikeImage.src = btn.dataset.image;
    if (bikeTitle) bikeTitle.textContent = btn.dataset.name;
    if (bikeDescription) bikeDescription.textContent = btn.dataset.description;
  }

  modelBtns.forEach(btn => {
    btn.addEventListener('click', () => {
      setActiveModel(btn);
      updateModelDetails(btn);
    });
  });

  // Navigation buttons
  const prevBtn = document.querySelector('.prev-btn');
  const nextBtn = document.querySelector('.next-btn');

  prevBtn?.addEventListener('click', () => {
    const activeGroup = document.querySelector('.model-group:not(.hidden)');
    const activeBtn = activeGroup.querySelector('.variant-btn.active');
    const prevBtn = activeBtn.previousElementSibling;
    
    if (prevBtn) {
      setActiveModel(prevBtn);
      updateModelDetails(prevBtn);
    }
  });

  nextBtn?.addEventListener('click', () => {
    const activeGroup = document.querySelector('.model-group:not(.hidden)');
    const activeBtn = activeGroup.querySelector('.variant-btn.active');
    const nextBtn = activeBtn.nextElementSibling;
    
    if (nextBtn) {
      setActiveModel(nextBtn);
      updateModelDetails(nextBtn);
    }
  });
}); 