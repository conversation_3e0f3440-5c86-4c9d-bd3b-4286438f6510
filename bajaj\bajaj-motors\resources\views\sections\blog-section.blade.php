<!-- Blog Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Section Header -->
        <div class="text-center mb-12">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Latest News & Stories</h2>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                Stay updated with the latest motorcycle news, riding tips, and community stories from the world of Bajaj.
            </p>
        </div>

        <!-- Blog Grid -->
        <div id="blog-grid" class="grid grid-cols-1 lg:grid-cols-10 gap-6">
            @if(isset($recentBlogs) && count($recentBlogs) > 0)
                @php
                    $featuredBlog = $featuredBlog ?? $recentBlogs->first();
                    $otherBlogs = $recentBlogs->where('id', '!=', $featuredBlog->id)->take(3);
                @endphp

                <!-- Featured Blog Text Column -->
                <div class="group cursor-pointer featured-text-column" style="grid-column: 1 / span 4;" onclick="window.location.href='{{ route('blogs.show', $featuredBlog->slug ?? '#') }}'">
                    <article class="overflow-hidden transition-all duration-300 h-full font-roboto">
                        <div class="p-6 lg:p-8">
                            <div class="mb-4">
                                <span class="text-sm text-gray-500 font-medium">
                                    {{ isset($featuredBlog->published_at) ? $featuredBlog->published_at->format('M j, Y') : 'Recent' }}
                                </span>
                            </div>
                            <h3 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors">
                                {{ $featuredBlog->title ?? 'Featured Blog Title' }}
                            </h3>
                            <p class="text-gray-600 mb-6 leading-relaxed">
                                {{ $featuredBlog->excerpt ?? 'Blog excerpt content goes here...' }}
                            </p>
                            <button class="inline-flex items-center bg-gray-900 text-white px-6 py-2 rounded-full font-medium hover:bg-gray-800 transition-colors group">
                                <span>LEARN MORE</span>
                                <svg class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </button>
                        </div>
                    </article>
                </div>

                <!-- Featured Blog Image Column -->
                <div class="group cursor-pointer featured-image-column" style="grid-column: 5 / span 6;" onclick="window.location.href='{{ route('blogs.show', $featuredBlog->slug ?? '#') }}'">
                    <div class="relative h-80 lg:h-96 overflow-hidden rounded-xl transition-all duration-300">
                        <img src="{{ $featuredBlog->image ?? asset('assets/blogs/default-blog.jpg') }}" 
                             alt="{{ $featuredBlog->title ?? 'Featured Blog' }}" 
                             class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500" 
                             loading="lazy" />
                        <div class="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"></div>
                    </div>
                </div>

                <!-- Bottom Row - Other Blogs -->
                <div class="lg:col-span-10" style="grid-column: 1 / -1;">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                        @foreach($otherBlogs as $blog)
                            <article class="group cursor-pointer" onclick="window.location.href='{{ route('blogs.show', $blog->slug ?? '#') }}'">
                                <div class="relative h-52 overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-all duration-300 mb-4">
                                    <img src="{{ $blog->image ?? asset('assets/blogs/default-blog.jpg') }}" 
                                         alt="{{ $blog->title }}" 
                                         class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500" 
                                         loading="lazy" />
                                </div>
                                <div class="text-center">
                                    <h4 class="text-sm font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                                        {{ $blog->title }}
                                    </h4>
                                </div>
                            </article>
                        @endforeach
                    </div>
                </div>
            @else
                <!-- Default content if no blogs -->
                <div class="group cursor-pointer featured-text-column" style="grid-column: 1 / span 4;">
                    <article class="overflow-hidden transition-all duration-300 h-full font-roboto">
                        <div class="p-6 lg:p-8">
                            <div class="mb-4">
                                <span class="text-sm text-gray-500 font-medium">Recent</span>
                            </div>
                            <h3 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors">
                                WHEELS OF MEMORY
                            </h3>
                            <p class="text-gray-600 mb-6 leading-relaxed">
                                Join us for the 12 months photography contest inviting riders to share bike photos symbolizing each of the 12 Indian months.
                            </p>
                            <button class="inline-flex items-center bg-gray-900 text-white px-6 py-2 rounded-full font-medium hover:bg-gray-800 transition-colors group">
                                <span>LEARN MORE</span>
                                <svg class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </button>
                        </div>
                    </article>
                </div>

                <div class="group cursor-pointer featured-image-column" style="grid-column: 5 / span 6;">
                    <div class="relative h-80 lg:h-96 overflow-hidden rounded-xl transition-all duration-300">
                        <img src="{{ asset('assets/blogsBg.jpg') }}" 
                             alt="Featured Blog" 
                             class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500" 
                             loading="lazy" />
                        <div class="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"></div>
                    </div>
                </div>

                <div class="lg:col-span-10" style="grid-column: 1 / -1;">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                        @for($i = 1; $i <= 3; $i++)
                            <article class="group cursor-pointer">
                                <div class="relative h-52 overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-all duration-300 mb-4">
                                    <img src="{{ asset('assets/blogsBg.jpg') }}" 
                                         alt="Blog {{ $i }}" 
                                         class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500" 
                                         loading="lazy" />
                                </div>
                                <div class="text-center">
                                    <h4 class="text-sm font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                                        Sample Blog Title {{ $i }}
                                    </h4>
                                </div>
                            </article>
                        @endfor
                    </div>
                </div>
            @endif
        </div>

        <!-- View All Blogs Button -->
        <div class="text-center mt-12">
            <a href="{{ route('blogs.index') }}" class="inline-block bg-gray-900 text-white px-8 py-3 rounded-full font-semibold hover:bg-gray-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                View All Stories
            </a>
        </div>
    </div>
</section>
