<header class="header-overlay fixed top-0 left-0 right-0 z-50">
    <!-- Top Bar -->
    <div class="bg-transparent py-2 top-bar">
        <div class="top-bar-content">
            <div class="top-bar-left">
                <img src="{{ asset('assets/golcha-logo.png') }}" alt="golcha_logo" class="top-bar-logo" />
                <span class="top-bar-text">GOLCHHA GROUP WITH LEGACY OF 100 YEAR</span>
            </div>
            <div class="top-bar-right">
                <!-- <svg class="top-bar-icon" viewBox="0 0 23 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6.89761 15.1618C8.28247 16.3099 10.0607 17 12.0001 17C16.4184 17 20.0001 13.4183 20.0001 9C20.0001 8.43095 19.9407 7.87578 19.8278 7.34036M6.89761 15.1618C5.12756 13.6944 4.00014 11.4789 4.00014 9C4.00014 4.58172 7.58186 1 12.0001 1C15.8494 1 19.0637 3.71853 19.8278 7.34036M6.89761 15.1618C8.85314 14.7147 11.1796 13.7828 13.526 12.4281C16.2564 10.8517 18.4773 9.01248 19.8278 7.34036M6.89761 15.1618C4.46844 15.7171 2.61159 15.5243 1.99965 14.4644C1.36934 13.3726 2.19631 11.5969 3.99999 9.70898M19.8278 7.34036C21.0796 5.79041 21.5836 4.38405 21.0522 3.46374C20.5134 2.53051 19.0095 2.26939 16.9997 2.59929" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg> -->
                <span class="top-bar-text">International website</span>
            </div>
        </div>
    </div>

    <!-- Main Navigation -->
    <div class="bg-transparent py-2 font-roboto lg:px-[150px] px-4">
        <nav class="floating-navbar bg-white my-4 px-6">
            <!-- Mobile Navigation -->
            <div class="lg:hidden flex items-center justify-between py-4">
                <!-- Mobile Menu Button -->
                <button class="flex items-center justify-center w-8 h-8" id="mobile-menu-btn">
                    <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>

                <!-- Mobile Logo -->
                <img class="h-12" src="{{ asset('assets/logo.png') }}" alt="logo" />

                <!-- Mobile BIKES Button -->
                <button class="text-sm font-medium text-gray-700 flex items-center space-x-1" id="mobile-bikes-btn">
                    <span>BIKES</span>
                    <svg class="w-4 h-4 transition-transform duration-200" id="mobile-bikes-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden lg:flex justify-evenly items-center py-4 text-base">
                <!-- Left Navigation Items -->
                <div class="flex items-center space-x-8">
                    <!-- Motorcycles Dropdown -->
                    @include('partials.navigation.motorcycles-dropdown')
                    
                    <a href="#" class="text-gray-700 text-sm hover:text-blue-600 transition-colors duration-200">SHOWROOMS</a>
                    <a href="#" class="text-gray-700 text-sm hover:text-blue-600 transition-colors duration-200">WORKSHOPS</a>
                    <a href="#" class="text-gray-700 text-sm hover:text-blue-600 transition-colors duration-200">EVENTS</a>
                </div>

                <!-- Center Logo -->
                <img class="h-[72px] px-4" src="{{ asset('assets/logo.png') }}" alt="logo" />

                <!-- Right Navigation Items -->
                <div class="flex text-sm items-center space-x-8">
                    <a href="#" class="text-gray-700 hover:text-blue-600 transition-colors duration-200">BOOK TEST RIDE</a>
                    <a href="{{ route('about') }}" class="text-gray-700 hover:text-blue-600 transition-colors duration-200">ABOUT US</a>
                    <a href="#" class="text-gray-700 hover:text-blue-600 transition-colors duration-200">NEWS</a>

                    <!-- Media Center Dropdown -->
                    @include('partials.navigation.media-dropdown')
                </div>
            </div>
        </nav>

        <!-- Mobile Menus -->
        @include('partials.navigation.mobile-menus')
    </div>
</header>
