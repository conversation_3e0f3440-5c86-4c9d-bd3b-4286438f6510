<?php

namespace App\Http\Controllers;

use App\Models\Blog;
use Illuminate\Http\Request;

class BlogController extends Controller
{
    /**
     * Display the blogs listing page
     */
    public function index()
    {
        $blogs = Blog::where('status', 'published')
            ->orderBy('published_at', 'desc')
            ->paginate(12);

        $featuredBlogs = Blog::where('featured', true)
            ->where('status', 'published')
            ->limit(3)
            ->get();

        return view('blogs.index', compact('blogs', 'featuredBlogs'));
    }

    /**
     * Display a single blog post
     */
    public function show($slug)
    {
        $blog = Blog::where('slug', $slug)
            ->where('status', 'published')
            ->firstOrFail();

        // Get related blogs
        $relatedBlogs = Blog::where('status', 'published')
            ->where('id', '!=', $blog->id)
            ->where(function($query) use ($blog) {
                $query->where('category', $blog->category)
                      ->orWhereJsonContains('tags', $blog->tags);
            })
            ->limit(3)
            ->get();

        return view('blogs.show', compact('blog', 'relatedBlogs'));
    }

    /**
     * Get blogs data for AJAX requests
     */
    public function getBlogsData(Request $request)
    {
        $limit = $request->get('limit', 4);
        $featured = $request->get('featured', false);

        $query = Blog::where('status', 'published');

        if ($featured) {
            $query->where('featured', true);
        }

        $blogs = $query->orderBy('published_at', 'desc')
            ->limit($limit)
            ->get();

        return response()->json([
            'blogs' => $blogs,
            'success' => true
        ]);
    }
}
