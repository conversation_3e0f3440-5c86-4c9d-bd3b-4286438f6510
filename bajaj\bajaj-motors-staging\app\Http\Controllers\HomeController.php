<?php

namespace App\Http\Controllers;

use App\Models\Bike;
use App\Models\Blog;
use App\Models\Experience;
use App\Models\HeroSlide;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Display the home page with all dynamic content
     */
    public function index()
    {
        // Get hero carousel slides
        $heroSlides = HeroSlide::where('active', true)
            ->orderBy('order')
            ->get();

        // Get bike brands with their models
        $bikeBrands = Bike::with('brand')
            ->select('brand_name')
            ->distinct()
            ->get()
            ->groupBy('brand_name');

        // Get featured bikes for carousel (one from each brand)
        $featuredBikes = Bike::where('featured', true)
            ->with(['colors', 'variants'])
            ->orderBy('brand_name')
            ->get();

        // Get recent blogs for blog section
        $recentBlogs = Blog::where('status', 'published')
            ->orderBy('published_at', 'desc')
            ->limit(4)
            ->get();

        // Get featured blog
        $featuredBlog = Blog::where('featured', true)
            ->where('status', 'published')
            ->first();

        // Get experiences for carousel
        $experiences = Experience::where('active', true)
            ->orderBy('priority')
            ->get();

        return view('home', compact(
            'heroSlides',
            'bikeBrands', 
            'featuredBikes',
            'recentBlogs',
            'featuredBlog',
            'experiences'
        ));
    }

    /**
     * Get bikes data for AJAX requests
     */
    public function getBikesData(Request $request)
    {
        $brand = $request->get('brand');
        $category = $request->get('category');

        $query = Bike::with(['colors', 'variants']);

        if ($brand) {
            $query->where('brand_name', $brand);
        }

        if ($category) {
            $query->where('category', $category);
        }

        $bikes = $query->get();

        return response()->json([
            'bikes' => $bikes,
            'success' => true
        ]);
    }
}
