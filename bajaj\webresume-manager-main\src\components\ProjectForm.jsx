"use client";

import { deleteProject, getProjectById } from "@/actions/project";
import { Button } from "@/components/ui/button";
import { useSession } from "next-auth/react";
import { CldUploadWidget } from "next-cloudinary";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";

const ProjectForm = ({ projectId }) => {
  const router = useRouter();
  const [errors, setErrors] = useState({});
  const [success, setSuccess] = useState(false);
  const { data: session, status } = useSession();

  useEffect(() => {
    if (success) {
      projectId
        ? toast.success("Project updated successfully!")
        : toast.success("Project added successfully!");
      router.push("/projects");
    }
  }, [success, router]);

  //handling form data with image
  const [img, setImg] = useState("");
  const [formData, setFormData] = useState({
    title: "",
    desc: "",
    category: "choose",
    githubLink: "",
    liveLink: "",
  });

  //get details of existing project in case of edit
  useEffect(() => {
    const getProject = async () => {
      if (projectId) {
        const project = await getProjectById(projectId);
        setFormData({
          title: project.title,
          category: project.category,
          desc: project.desc,
          githubLink: project.githubLink,
          liveLink: project.liveLink,
        });
        setImg(project.image);
      }
    };

    getProject();
  }, [projectId]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    const userId = session.user.userId;
    //console.log(session);
    const res = await fetch(`/api/project/${userId}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        title: formData.title,
        desc: formData.desc,
        category: formData.category,
        githubLink: formData.githubLink,
        liveLink: formData.liveLink,
        image: img || "",
      }),
    });

    const data = await res.json();
    if (res.ok) {
      //console.log("Post created:", data);
      setFormData({
        title: "",
        desc: "",
        category: "choose",
        githubLink: "",
        liveLink: "",
      });
      setImg("");
      setSuccess(true);
      setErrors({});

      //delete old project after updating
      if (projectId) {
        await deleteProject(projectId);
      }
    } else {
      //console.log("errror", data);
      setErrors(data.error || { general: [data.error] });
      setSuccess(false);
    }
  };

  if (status === "loading") return <p>Loading...</p>;

  return (
    <div className="px-4 md:px-8 xl:px-16">
      <div className="flex items-center gap-16 mb-12">
        <h1 className="text-3xl font-semibold">
          {projectId ? "Update Project" : "Add New Project"}
        </h1>
      </div>

      <form onSubmit={handleSubmit} className="flex flex-col gap-6">
        <div className="flex flex-col">
          <label htmlFor="title">Title</label>
          <input
            type="text"
            name="title"
            placeholder="Enter Project TItle"
            value={formData.title}
            onChange={(e) =>
              setFormData({
                ...formData,
                title: e.target.value,
              })
            }
            className="border border-gray-400 px-2 py-1 outline-none"
            required
          />
          {errors.title && <p className="text-red-500">{errors.title[0]}</p>}
        </div>

        <div className="flex flex-col">
          <label htmlFor="category">Category</label>
          <select
            id="category"
            name="category"
            value={formData.category}
            onChange={(e) =>
              setFormData({
                ...formData,
                category: e.target.value,
              })
            }
            className="border border-gray-400 px-2 py-1 outline-none"
            required
          >
            <option disabled hidden value="choose">
              Choose category
            </option>
            <option value="mobile">mobile</option>
            <option value="frontend">frontend</option>
            <option value="backend">backend</option>a
          </select>
          {errors.category && (
            <p className="text-red-500">{errors.category[0]}</p>
          )}
        </div>

        <CldUploadWidget
          uploadPreset="portfolio-backend"
          onSuccess={(result) => setImg(result?.info.secure_url)}
        >
          {({ open }) => {
            return (
              <div className="flex flex-col">
                <label htmlFor="image">Image</label>
                <Image
                  src={img || "/no-image.png"}
                  width={140}
                  height={100}
                  alt="image"
                  className="w-20 h-auto rounded-lg"
                />
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    open();
                  }}
                  name="imageUrl"
                  className="border border-gray-400 px-2 py-1 outline-none cursor-pointer"
                >
                  Choose Image
                </button>
              </div>
            );
          }}
        </CldUploadWidget>

        <div className="flex flex-col">
          <label htmlFor="desc">Description</label>
          <textarea
            name="desc"
            cols="40"
            rows="8"
            value={formData.desc}
            onChange={(e) =>
              setFormData({
                ...formData,
                desc: e.target.value,
              })
            }
            placeholder="Write your Project description here"
            className="border border-gray-400 px-2 py-1 outline-none"
            required
          ></textarea>
          {errors.desc && <p className="text-red-500">{errors.desc[0]}</p>}
        </div>

        <div className="flex flex-col">
          <label htmlFor="githubLink">Github Url</label>
          <input
            type="text"
            name="githubLink"
            placeholder="https://github.com/<username>/<repo-name>"
            value={formData.githubLink}
            onChange={(e) =>
              setFormData({
                ...formData,
                githubLink: e.target.value,
              })
            }
            className="border border-gray-400 px-2 py-1 outline-none"
          />
          {errors.githubLink && (
            <p className="text-red-500">{errors.githubLink[0]}</p>
          )}
        </div>

        <div className="flex flex-col">
          <label htmlFor="liveLink">Live Link</label>
          <input
            type="text"
            name="liveLink"
            placeholder="https://example.com"
            value={formData.liveLink}
            onChange={(e) =>
              setFormData({
                ...formData,
                liveLink: e.target.value,
              })
            }
            className="border border-gray-400 px-2 py-1 outline-none"
          />
          {errors.liveLink && (
            <p className="text-red-500">{errors.liveLink[0]}</p>
          )}
        </div>

        <Button
          type="submit"
          className="mb-10 bg-gray-800 text-white cursor-pointer hover:text-stone-200"
        >
          {projectId ? "Update Project" : "Add Project"}
        </Button>
      </form>
    </div>
  );
};

export default ProjectForm;
