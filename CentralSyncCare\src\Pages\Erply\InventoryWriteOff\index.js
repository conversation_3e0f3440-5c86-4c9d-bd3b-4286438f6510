import React, { useEffect, useState, useRef } from "react";
import {
  <PERSON><PERSON>or<PERSON>,
  Check,
  Clear,
  Close,
  Download,
  FilterList,
} from "@mui/icons-material";
import TableComponent from "../TableComponent";
import httpclient from "../../../Utils";
import {
  Box,
  Button,
  Card,
  Collapse,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  styled,
  TextField,
  Snackbar,
  Autocomplete,
  InputAdornment,
  Checkbox,
} from "@mui/material";
import MuiAlert from "@mui/material/Alert";
import { useLocation, useNavigate } from "react-router";
import useTokenRefresh from "../../../Hooks/useTokenRefresh";
import Footer from "../../../Components/Footer";
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import ViewInventoryWriteOffDetail from "./ViewInventoryWriteOffDetail";


const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
const checkedIcon = <CheckBoxIcon fontSize="small" />;


//import { useLocation } from "react-router-dom";

const Alert = React.forwardRef(function Alert(props, ref) {
  return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});



const columns = [
  //{ id: "checkColumn", name: " " },
  { id: "inventoryWriteOffID", name: "Inventory WriteOff ID" },
  { id: "netsuiteID", name: "NetSuite ID" },
  //{ id: "comments", name: "Cause" },
  { id: "clientCode", name: "Client Code" },
  { id: "date", name: "Date" },
  { id: "comments", name: "Comments" },
  { id: "erply_warehouse", name: "Erply Warehouse" },
  { id: "pendingProcess", name: "Pending Process?" },
  { id: "lastModified", name: "Last Modified Date" },
];

const FilteredBox = styled(Box)(({ theme }) => ({
  background: "#f9f9f9",
  padding: "5px 10px",
  borderRadius: "5px",
  "& p": {
    margin: "3px 0",
    marginRight: "10px",
    display: "inline-block",
    background: "#dedede",
    borderRadius: "10px",
    padding: "2px 5px",
  },
  "& svg": {
    fontSize: "15px",
    cursor: "pointer",
    position: "relative",
    top: "3px",
    background: theme.palette.primary.dark,
    color: "#fff",
    borderRadius: "50%",
    padding: "2px",
    marginLeft: "2px",
  },
}));

const Header = styled("div")(({ theme }) => ({
  "& h1": {
    color: theme.palette.primary.dark,
    margin: "0",
  },
}));

const configRowPerPage = JSON.parse(localStorage.getItem("configRowPerPage"));

const ErplyInventoryWriteOff = (props) => {

  const { getTokenRefreshed: refresh, open: tokenOpen, overlay: overlay, setOpen: setTokenOpen, message: tokenMessage, messageState: tokenMessageState } = useTokenRefresh();

  const location = useLocation();
  const navigate = useNavigate();
  const buttonRef = useRef(null);

  const [openViewDialog, setOpenViewDialog] = useState(false);
  const [viewDetails, setViewDetails] = useState({});
  const [openStatusDialog, setOpenStatusDialog] = useState(false);
  const [customStatus, setCustomStatus] = useState([]);
  const [exceptionStatus, setExceptionStatus] = useState([]);
  const [rows, setRows] = useState([]);
  const [exportRows, setExportRows] = useState("");
  const [rowChecked, setRowChecked] = useState([]);
  const [company, setCompany] = useState([]);
  const [selectedChecked, setSelectedChecked] = useState([]);
  const [orderChecked, setOrderChecked] = useState([]);
  const [status, setStatus] = useState("");
  const [selected, setSelected] = useState([]);
  const [selected1, setSelected1] = useState([]);
  const [buttonLoader, setButtonLoader] = useState(false);
  const [backdropLoader, setBackdropLoader] = useState(false);
  const [loading, setLoading] = useState(false);
  const [singleLoading, setSingleLoading] = useState(false);
  const [direction, setDirection] = useState(false);
  const [currentColumn, setCurrentColumn] = useState("");
  const [page, setPage] = useState(1);
  const [from, setFrom] = useState(1);
  const [to, setTo] = useState(
    configRowPerPage && configRowPerPage
      ? configRowPerPage && configRowPerPage
      : 20
  );

  const [rowsPerPage, setRowsPerPage] = useState(
    configRowPerPage && configRowPerPage
      ? configRowPerPage && configRowPerPage
      : 20
  );
  const [total, setTotal] = useState("");
  const [filterOpen, setFilterOpen] = useState(false);
  const [orderTypes, setOrderTypes] = useState([]);
  const [orderTypesNames, setOrderTypesNames] = useState([]);
  const [open, setOpen] = useState(false);
  const [message, setMessage] = useState("");
  const [messageState, setMessageState] = useState("");
  const [companyList, setCompanyList] = useState([]);
  const [filterData, setFilterData] = useState({

    number: "",
    orderTypesName: "",
    orderTypes: "",
    inventoryWriteOffID: "",
    netsuiteID: "",
    comments: "",
    pendingProcess: "",
    startDate: "",
    endDate: "",
    remove: false,
  });

  const [submittedData, setSubmittedData] = useState({

    number: "",
    orderTypesName: "",
    orderTypes: "",
    inventoryWriteOffID: "",
    netsuiteID: "",
    comments: "",
    pendingProcess: "",
    startDate: "",
    endDate: "",

    submit: false,
  });

  useEffect(() => {
    if (

      filterData.number === "" ||
      filterData.orderTypesName === "" ||
      filterData.orderTypes === "" ||
      filterData.inventoryWriteOffID === "" ||
      filterData.netsuiteID === "" ||
      filterData.comments === "" ||
      filterData.pendingProcess === "" ||
      filterData.startDate === "" ||
      filterData.endDate === ""

    ) {
      setSubmittedData({
        ...submittedData,
        submit: false,
      });
    }

    if (filterData.number === " ") filterData.number = "";
    if (filterData.orderTypesName === " ") filterData.orderTypesName = "";
    if (filterData.orderTypes === " ") filterData.orderTypes = "";
    if (filterData.inventoryWriteOffID === " ") filterData.inventoryWriteOffID = "";
    if (filterData.netsuiteID === " ") filterData.netsuiteID = "";
    if (filterData.comments === " ") filterData.comments = "";
    if (filterData.pendingProcess === " ") filterData.pendingProcess = "";
    if (filterData.startDate === " ") filterData.startDate = "";
    if (filterData.endDate === " ") filterData.endDate = "";

    filterData.remove === true && handleFilter();
  }, [filterData]);

  useEffect(() => {
    let currentpolicy = JSON.parse(localStorage.getItem("erply_inventory_writeoff_filter"));
    currentpolicy !== null && setFilterData(currentpolicy);

    currentpolicy == null
      ? getInventoryWriteOff()
      :
      currentpolicy.number == "" &&
        currentpolicy.orderTypesName == "" &&
        currentpolicy.orderTypes == "" &&
        currentpolicy.inventoryWriteOffID == "" &&
        currentpolicy.netsuiteID == "" &&
        currentpolicy.comments == "" &&
        currentpolicy.pendingProcess == "" &&
        currentpolicy.startDate == "" &&
        currentpolicy.endDate == "" &&

        currentpolicy.removed == false
        ? getInventoryWriteOff()
        : console.log("erply orders");
  }, []);

  useEffect(() => {
    if (location.state !== null) {
      if (location.state?.id) {
        filterData.number = location.state.value
        setTimeout(() => {
          handleFilter();
          navigate("#", { replace: true });
          handleView(location.state.id);
        }, 1500);
      }
      if (location.state?.startDate) {
        filterData.startDate = location.state.startDate
        filterData.endDate = location.state.endDate
        setTimeout(() => {
          handleFilter();
          navigate("#", { replace: true });
        }, 1500);
      }
      setTimeout(() => {
        navigate("#", { replace: true });
      }, 1500);
    }
  }, [location.state]);


  const getInventoryWriteOff = () => {
    setLoading(true);
    httpclient
      .get(`${props.request_name}&pagination=${rowsPerPage}&page=${page}`)
      .then(({ data }) => {
        if (data.status === 200) {
          setRows(data.data);
          setOrderTypes(data.orderTypes);
          setTotal(data.meta.total);
          setRowsPerPage(parseInt(data.meta.per_page));
          setPage(data.meta.current_page);
          setFrom(data.meta.from);
          setTo(data.meta.to);
          setLoading(false);
        } else {
          setOpen(true);
          setMessage(data.message);
          setMessageState("error");
          setLoading(false);
        }

      }).catch((err) => {
        if (err.response.status === 401) {
          refresh();
          setOpen(tokenOpen);
          setMessage(tokenMessage);
          setMessageState("error");
        } else if (err.response.status === 422) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setLoading(false);
        } else if (err.response.status === 400) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setLoading(false);

        } else {
          setOpen(true);
          setMessage(err.response.data.message);
          setMessageState("error");
          setLoading(false);
        }
      })
  };


  const handleView = (row) => {
    setSingleLoading(true);
    setOpenViewDialog(true);
    httpclient
      .get(`${props.request_name}/${row.inventoryWriteOffID || row}`)
      .then(({ data }) => {
        if (data) {
          setViewDetails(data.data);
          setSingleLoading(false);
        }
        else {
          setOpen(true);
          setMessage(data.message);
          setMessageState("error");
          setLoading(false);
        }

      }).catch((err) => {
        if (err.response.status === 401) {
          refresh();
          setOpen(tokenOpen);
          setMessage(tokenMessage);
          setMessageState("error");
        } else if (err.response.status === 422) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setLoading(false);
        } else if (err.response.status === 400) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setLoading(false);

        } else {
          setOpen(true);
          setMessage(err.response.data.message);
          setMessageState("error");
          setLoading(false);
        }
      })
  };

  const sendDetails = (callback) => {
    if (callback.open === false) {
      setOpenViewDialog(false);
      setViewDetails({});
    }
    if (callback.refetch === true) {
      handleView(callback.salesDocumentID);
      setTimeout(() => {
        handleFilter();
      }, 1000);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;

    if (name === "orderTypes") {
      setFilterData({
        ...filterData,
        orderTypesName: value,
        remove: false,
      });
    }

    if (name === "soldTo") {
      setFilterData({
        ...filterData,
        soldToName: value,
        remove: false,
      });
    }
    if (name === "customStatus") {
      setFilterData({
        ...filterData,
        customStatusName: value,
        remove: false,
      });
    }
    if (name === "exceptionStatus") {
      setFilterData({
        ...filterData,
        exceptionStatusName: value,
        remove: false,
      });
    }
  };


  const handleChangeOrderTypes = (value) => {
    setOrderTypesNames(value);
    const ids = value.map((item) => item.name);
    const names = value.map((item) => item.name).join(", ");

    setFilterData({
      ...filterData,
      orderTypes: ids,
      orderTypesName: names,
      remove: false,
    });
  };


  const handleFilter = () => {
    setSubmittedData({
      ...submittedData,

      number: filterData.number,
      orderTypesName: filterData.orderTypesName,
      orderTypes: filterData.orderTypes,
      inventoryWriteOffID: filterData.inventoryWriteOffID,
      netsuiteID: filterData.netsuiteID,
      comments: filterData.comments,
      pendingProcess: filterData.pendingProcess,
      startDate: filterData.startDate,
      endDate: filterData.endDate,
      submit: true,
    });

    filterData.remove = true;

    localStorage.setItem("erply_inventory_writeoff_filter", JSON.stringify(filterData));


    setLoading(true);
    if (

      filterData.number ||
      filterData.orderTypesName ||
      filterData.orderTypes ||
      filterData.inventoryWriteOffID ||
      filterData.netsuiteID ||
      filterData.comments ||
      filterData.pendingProcess ||
      filterData.startDate ||
      filterData.endDate

    ) {
      const idParams = filterData.orderTypes !== "" ? filterData.orderTypes
        .map((id, index) => `filters[type][$in][${index}]=${id}`)
        .join("&") : `filters[type][$in][0]=`;

      httpclient
        .get(
          `${props.request_name}&filters[number][$eq]=${filterData.number
          }&filters[inventoryWriteOffID][$eq]=${filterData.inventoryWriteOffID
          }&filters[netsuiteID][$eq]=${filterData.netsuiteID
          }&filters[comments][$contains]=${filterData.comments
          }&filters[pendingProcess][$eq]=${filterData.pendingProcess
          }&filters[lastModified][$between][0]=${filterData.startDate
          }&filters[lastModified][$between][1]=${filterData.endDate
          }&pagination=${rowsPerPage}&page=${1}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setOrderTypes(data.orderTypes);
            setTotal(data.meta.total);
            setRowsPerPage(data.meta.per_page);
            setPage(data.meta.current_page);
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }
        });
    } else {
      getInventoryWriteOff();
    }
  };



  const hadleFilterOpen = () => {
    setFilterOpen((prev) => !prev);
  };

  const handleChangeFilter = (e) => {
    const { name, value } = e.target;
    setFilterData({
      ...filterData,
      [name]: value,
      remove: false,
    });
  };
  // console.log('filter data', filterData);

  const handleRemove = (data) => {

    if (data === "orderTypes") {
      filterData.orderTypesName = "";
      submittedData.orderTypesName = "";
      setOrderTypesNames([]);
    }
    if (data === "startDate") {
      setFilterData({
        ...filterData,
        startDate: "",
        endDate: "",
        remove: true,
      });
      setSubmittedData({
        ...submittedData,
        startDate: "",
        endDate: "",
      });
    } else {
      setFilterData({
        ...filterData,
        [data]: "",
        remove: true,
      });
      setSubmittedData({
        ...submittedData,
        [data]: "",
      });
    }
  };



  // const handleRowCheck = (e, row) => {
  //   console.log("row", row);
  //   var checkIfNotSame = false;
  //   selectedChecked.map((check) => {
  //     if (
  //       row.reportType !== check.reportType ||
  //       row.orderType !== check.orderType
  //     ) {
  //       return (checkIfNotSame = true);
  //     } else {
  //       return (checkIfNotSame = false);
  //     }
  //   });

  //   if (checkIfNotSame === true) {
  //     setButtonLoader(true);
  //     setTimeout(() => {
  //       alert("Please select only one type of exception order");
  //       setButtonLoader(false);
  //     }, 500);
  //   } else {
  //     setButtonLoader(false);
  //     const { checked } = e.target;
  //     if (checked === true) {
  //       setRowChecked([...rowChecked, row.orderID]);
  //       setCompany([...company, row.company.erpAccountCustomerID]);
  //       setSelectedChecked([...selectedChecked, row]);
  //       setOrderChecked([
  //         ...orderChecked,
  //         !row.orderProduct.some(
  //           (product) =>
  //             product.reasonCode === "3" ||
  //             product.reasonCode === "4" ||
  //             product.reasonCode === "5" ||
  //             product.reasonCode === "12" ||
  //             product.reasonCode === ""
  //         ) || accept(row),
  //       ]);
  //     } else {
  //       let newData = rowChecked.filter((check) => {
  //         return check !== row.orderID;
  //       });

  //       let newArr = selectedChecked.filter((select) => {
  //         return select.orderID !== row.orderID;
  //       });

  //       let newOrder = orderChecked.filter((order, index) => {
  //         return index !== rowChecked.indexOf(row.orderID);
  //       });
  //       let newArr1 = company.filter((order, index) => {
  //         return index !== rowChecked.indexOf(row.orderID);
  //       });

  //       // Update the 'company' state with the unique companies
  //       setCompany(newArr1);
  //       setRowChecked(newData);
  //       setSelectedChecked(newArr);
  //       setOrderChecked(newOrder);
  //     }
  //   }
  // };




  const handleSort = (column) => {
    setDirection((prevDirection) => !prevDirection);
    setCurrentColumn(column);
    setLoading(true);
    const idParams = filterData.orderTypes !== "" ? filterData.orderTypes
      .map((id, index) => `filters[type][$in][${index}]=${id}`)
      .join("&") : `filters[type][$in][0]=`;
    submittedData.submit
      ? httpclient
        .get(
          `${props.request_name}&filters[number][$eq]=${filterData.number
          }&filters[inventoryWriteOffID][$eq]=${filterData.inventoryWriteOffID
          }&filters[netsuiteID][$eq]=${filterData.netsuiteID
          }&filters[comments][$contains]=${filterData.comments
          }&filters[pendingProcess][$eq]=${filterData.pendingProcess
          }&filters[lastModified][$between][0]=${filterData.startDate
          }&filters[lastModified][$between][1]=${filterData.endDate
          }&sort[0]=${column}:${!direction ? "asc" : "desc"
          }&pagination=${rowsPerPage}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setTotal(data.meta.total);
            setRowsPerPage(parseInt(data.meta.per_page));
            setPage(data.meta.current_page);
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }
        })

      : httpclient
        .get(
          `${props.request_name}&sort[0]=${column}:${!direction ? "asc" : "desc"
          }&pagination=${rowsPerPage}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }

        });
  };

  const handleChangePage = (e, page) => {
    setLoading(true);
    const idParams = filterData.orderTypes !== "" ? filterData.orderTypes
      .map((id, index) => `filters[type][$in][${index}]=${id}`)
      .join("&") : `filters[type][$in][0]=`;
    submittedData.submit
      ? httpclient
        .get(
          `${props.request_name}&filters[number][$eq]=${filterData.number
          }&filters[inventoryWriteOffID][$eq]=${filterData.inventoryWriteOffID
          }&filters[netsuiteID][$eq]=${filterData.netsuiteID
          }&filters[comments][$contains]=${filterData.comments
          }&filters[pendingProcess][$eq]=${filterData.pendingProcess
          }&filters[lastModified][$between][0]=${filterData.startDate
          }&filters[lastModified][$between][1]=${filterData.endDate
          }${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''
          }&pagination=${rowsPerPage}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setTotal(data.meta.total);
            setRowsPerPage(parseInt(data.meta.per_page));
            setPage(data.meta.current_page);
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }

        })
      : httpclient
        .get(
          `${props.request_name
          }${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''}&pagination=${rowsPerPage}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setTotal(data.meta.total);
            setRowsPerPage(parseInt(data.meta.per_page));
            setPage(data.meta.current_page);
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }

        });
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(+event.target.value);
    setLoading(true);

    localStorage.setItem("configRowPerPage", event.target.value);
    const idParams = filterData.orderTypes !== "" ? filterData.orderTypes
      .map((id, index) => `filters[type][$in][${index}]=${id}`)
      .join("&") : `filters[type][$in][0]=`;
    submittedData.submit
      ? httpclient
        .get(
          `${props.request_name}&filters[number][$eq]=${filterData.number
          }&filters[inventoryWriteOffID][$eq]=${filterData.inventoryWriteOffID
          }&filters[netsuiteID][$eq]=${filterData.netsuiteID
          }&filters[comments][$contains]=${filterData.comments
          }&filters[pendingProcess][$eq]=${filterData.pendingProcess
          }&filters[lastModified][$between][0]=${filterData.startDate
          }&filters[lastModified][$between][1]=${filterData.endDate
          }${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''
          }&pagination=${+event.target.value}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setTotal(data.meta.total);
            setRowsPerPage(parseInt(data.meta.per_page));
            setPage(data.meta.current_page);
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }
        })
      : httpclient
        .get(
          `${props.request_name
          }${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''}&pagination=${+event.target.value}&page=${1}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setTotal(data.meta.total);
            setRowsPerPage(parseInt(data.meta.per_page));
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setPage(data.meta.current_page);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }
        })
  };


  const handleClose = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }
    setOpen(false);
    setTokenOpen(false);
  };


  return (
    <div>
      <Grid container spacing={2}>
        <Grid item md={8} xs={12}>
          <Header>
            <h1>List Inventory WriteOff (ERPLY)</h1>
          </Header>
        </Grid>
        <Grid
          item
          md={4}
          xs={12}
          display="flex"
          alignItems="center"
          justifyContent="flex-end"
        >
          <Button color="primary" variant="contained" onClick={hadleFilterOpen}>
            Filter <FilterList style={{ marginLeft: "5px" }} fontSize="small" />
          </Button>
        </Grid>

        {/* Filter */}
        <Grid item xs={12}>
          <Collapse in={filterOpen}>
            <Card>
              <Box p={4}>
                <Grid container spacing={2}>

                  {/* <Grid item xs={12} md={4}>
                    <InputLabel>OrderTypes</InputLabel>
                    <Autocomplete
                      options={orderTypes}
                      onChange={(event, newValue) => {
                        handleChangeOrderTypes(newValue);
                      }}
                      multiple
                      id="checkboxes-tags-demo"
                      disableCloseOnSelect
                      value={orderTypesNames}
                      getOptionLabel={(option) => option.name}
                      renderOption={(props, option, { selected }) => (
                        <li {...props}>
                          <Checkbox
                            icon={icon}
                            checkedIcon={checkedIcon}
                            style={{ marginRight: 8 }}
                            checked={selected}
                          />
                          {option.name}
                        </li>
                      )}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          onChange={handleChange}
                          onKeyDown={(e) => {
                            if (e.key === "Enter") handleFilter();
                          }}
                          value={filterData.orderTypesName}
                          variant="outlined"
                          name="orderTypes"
                        // label="Company"
                        />
                      )}
                    />
                  </Grid> */}

                  {/* <Grid item xs={12} md={4}>
                    <InputLabel>Order Number</InputLabel>
                    <TextField
                      name="number"
                      value={filterData.number}
                      onChange={handleChangeFilter}
                      onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                      variant="outlined"
                      fullWidth
                    />
                  </Grid> */}
                  <Grid item xs={12} md={4}>
                    <InputLabel>Inventory WriteOff ID</InputLabel>
                    <TextField
                      name="inventoryWriteOffID"
                      value={filterData.inventoryWriteOffID}
                      onChange={handleChangeFilter}
                      onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                      variant="outlined"
                      fullWidth
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <InputLabel>NetSuite ID</InputLabel>
                    <TextField
                      name="netsuiteID"
                      value={filterData.netsuiteID}
                      onChange={handleChangeFilter}
                      onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                      variant="outlined"
                      fullWidth
                    />

                  </Grid>
                  <Grid item xs={12} md={4}>
                    <InputLabel>Comments</InputLabel>
                    <TextField
                      name="comments"
                      value={filterData.comments}
                      onChange={handleChangeFilter}
                      onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                      variant="outlined"
                      fullWidth
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <InputLabel>Pending Process</InputLabel>
                    <FormControl fullWidth>
                      <Select
                        value={filterData.pendingProcess}
                        name="pendingProcess"
                        onChange={handleChangeFilter}
                      >
                        <MenuItem value={""}>Select</MenuItem>
                        <MenuItem value={"0"}>No</MenuItem>
                        <MenuItem value={"1"}>Yes</MenuItem>

                      </Select>
                    </FormControl>

                  </Grid>
                  <Grid item xs={12} md={4}>
                    <InputLabel>Start Date</InputLabel>
                    <TextField
                      variant="outlined"
                      name="startDate"
                      type="date"
                      value={filterData.startDate}
                      onChange={(e) => handleChangeFilter(e)}
                      fullWidth
                      InputLabelProps={{
                        shrink: true,
                      }}
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <InputLabel>End Date</InputLabel>
                    <TextField
                      variant="outlined"
                      name="endDate"
                      type="date"
                      value={filterData.endDate}
                      onChange={(e) => handleChangeFilter(e)}
                      fullWidth
                      InputLabelProps={{
                        shrink: true,
                      }}
                    />
                  </Grid>


                  <Grid item xs={12}>
                    <Box textAlign={"right"}>
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={handleFilter}
                      >
                        Filter{" "}
                        <ArrowForward
                          fontSize="small"
                          style={{ marginLeft: "5px" }}
                        />
                      </Button>
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            </Card>
          </Collapse>
        </Grid>

        {
          submittedData.orderTypes ||
            submittedData.number ||
            submittedData.inventoryWriteOffID ||
            submittedData.netsuiteID ||
            submittedData.comments ||
            submittedData.pendingProcess ||
            submittedData.startDate ||
            submittedData.endDate
            ? (
              <Grid item xs={12}>
                <FilteredBox>
                  <span>Filtered: </span>

                  {/* {submittedData.orderTypes && (
                    <p>
                      <span>Sites: {submittedData.orderTypesName}</span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("orderTypes")}
                      />
                    </p>
                  )}
                  {submittedData.number && (
                    <p>
                      <span>Order Number: {submittedData.number}</span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("number")}
                      />
                    </p>
                  )} */}
                  {submittedData.inventoryWriteOffID && (
                    <p>
                      <span>Inventory WriteOff ID: {submittedData.inventoryWriteOffID}</span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("inventoryWriteOffID")}
                      />
                    </p>
                  )}
                  {submittedData.pendingProcess && (
                    <p>
                      <span>Pending Process?: {submittedData.pendingProcess === 1 ? "Yes" : "No"}</span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("pendingProcess")}
                      />
                    </p>
                  )}
                  {submittedData.netsuiteID && (
                    <p>
                      <span>NetSuite ID: {submittedData.netsuiteID}</span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("netsuiteID")}
                      />
                    </p>
                  )}
                  {submittedData.comments && (
                    <p>
                      <span>Comments: {submittedData.comments}</span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("comments")}
                      />
                    </p>
                  )}
                  {(submittedData.startDate || submittedData.endDate) && (
                    <p>
                      <span>
                        Last Modified Date Range: {submittedData.startDate} -{" "}
                        {submittedData.endDate}
                      </span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("startDate")}
                      />
                    </p>
                  )}

                </FilteredBox>
              </Grid>
            ) : (
              <Box></Box>
            )}
        {/* Filter */}



        <Grid item xs={12}>
          <TableComponent
            columns={columns}
            rows={rows}
            sort={true}
            handleView={handleView}
            handleSort={handleSort}
            loading={loading}
            handleChangeRowsPerPage={handleChangeRowsPerPage}
            handleChangePage={handleChangePage}
            // handleRowCheck={handleRowCheck}
            rowChecked={rowChecked}
            buttonLoader={buttonLoader}
            direction={direction}
            currentColumn={currentColumn}
            page={page}
            total={total && total}
            fromTable={from}
            toTable={to}
            rowsPerPage={rowsPerPage}
            filterData={filterData}
          />
        </Grid>
        <Footer overlay={overlay || props.overlayNew} />
      </Grid>

      {openViewDialog && (
        <ViewInventoryWriteOffDetail
          singleLoading={singleLoading}
          viewDetails={viewDetails}
          sendDetails={sendDetails}
        />
      )}


      {/* {openStatusDialog && (
        <StatusDialog
          sendChangeOrder={sendChangeOrder}
          status={status}
          selectedChecked={selectedChecked}
          viewDetails={viewDetails}
        />
      )} */}

      {/* {backdropLoader && <BackdropLoader />} */}

      <Snackbar
        autoHideDuration={3000}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
        open={open || tokenOpen}
        onClose={handleClose}
      >
        <Alert
          onClose={handleClose}
          severity={messageState || tokenMessageState}
          sx={{ width: "100%" }}
        >
          {message || tokenMessage}
        </Alert>
      </Snackbar>
    </div>
  );
};

export default ErplyInventoryWriteOff;
