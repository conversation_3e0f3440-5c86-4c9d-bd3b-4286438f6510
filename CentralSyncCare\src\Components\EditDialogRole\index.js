import { Close, <PERSON><PERSON><PERSON>, <PERSON>move } from "@mui/icons-material";
import {
    Box,
    Button,
    Checkbox,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    FormControl,
    Grid,
    IconButton,
    InputLabel,
    MenuItem,
    Select,
    Switch,
    TextField,
    Tooltip,
} from "@mui/material";
import { useEffect, useState } from "react";
import * as React from 'react';
import AddBoxIcon from '@mui/icons-material/AddBox';
import IndeterminateCheckBoxIcon from '@mui/icons-material/IndeterminateCheckBox';
import SvgIcon from '@mui/material/SvgIcon';
import { styled } from '@mui/material/styles';
import { SimpleTreeView } from '@mui/x-tree-view/SimpleTreeView';
import { TreeItem, treeItemClasses } from '@mui/x-tree-view/TreeItem';

const FlexContent = styled("div")(({ theme }) => ({
    display: "flex",
    flexDirection: "row",
    fontSize: "17px",
    marginBottom: "10px",
    alignItems: "flex-start",
}));

const FlexInnerTitle = styled("div")(({ theme }) => ({
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    minWidth: "280px",
    maxWidth: "250px",
    fontWeight: "600",
}));

const Values = styled("div")(({ theme }) => ({
    marginLeft: "15px",
    fontWeight: "500",
    color: theme.palette.primary.dark,
}));


const StyledHeaderTitle = styled(DialogTitle)(({ theme }) => ({
    background: theme.palette.primary.main,
    color: "#fff",
    position: "relative",
    "& button": {
        position: "absolute",
        right: "15px",
        top: "15px",
        color: "#fff",
    },
}));


const CustomTreeItem = styled(TreeItem)(({ theme }) => ({
    [`& .${treeItemClasses.iconContainer}`]: {
        '& .close': {
            opacity: 0.3,
        },
    },
}));

const EditDialogRole = (props) => {
    const [dialogDetails, setDialogDetails] = useState({
        open: true,
        success: false,
    });
    const [errors, setErrors] = useState({});
    const [formData, setFormData] = useState({
        name: props.viewDetails.role_name ? props.viewDetails.role_name : '',
        // status: 1,
        menuPermissions: [],
        modulePermissions: [],
    });

    const handleSwitch = (event) => {
        const isChecked = event.target.checked;
        setFormData({ ...formData, status: isChecked === true ? 1 : 0 });
    };

    const [checkedMenu, setCheckedMenu] = useState(() =>
        props.viewDetails.menuPermission?.reduce((acc, module) => {
            acc[module.id] = module.status === 1 ? true : false;
            if (module.sub_modules) {
                module.sub_modules.forEach((sub) => {
                    acc[sub.id] = sub.status === 1 ? true : false;
                });
            }
            return acc;
        }, {})
    );

    const [checked, setChecked] = useState(() =>
        props.viewDetails.modulePermission?.reduce((acc, module) => {
            acc[module.id] = module.status === 1 ? true : false;
            if (module.sub_modules) {
                module.sub_modules.forEach((sub) => {
                    acc[sub.id] = sub.status === 1 ? true : false;
                });
            }
            return acc;
        }, {})
    );

    useEffect(() => {
        if (props.viewDetails) {
            const formatPermissions = (permissions) => {
                return Object.entries(permissions).map(([key, value]) => ({
                    id: Number(key),
                    status: value ? 1 : 0,
                }));
            };
            let name = formData.name
            let status = formData.status
            setFormData({
                name: name,
                // status: status,
                menuPermissions: formatPermissions(checkedMenu || {}),
                modulePermissions: formatPermissions(checked || {}),
            });
        }
    }, [props.viewDetails, checked, checkedMenu]);
    

    useEffect(() => {
        props.sendEdit(dialogDetails, formData);
    }, [dialogDetails, formData]);

    const handleClose = () => {
        setDialogDetails({
            ...dialogDetails,
            open: false,
        });
    };

    const handleYes = () => {       
        setDialogDetails({
            ...dialogDetails,
            open: false,
            success: true,
        });
    };

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData((prevData) => ({
            ...prevData,
            [name]: value,
        }));
    };

    const handleCheckMenu = (id, subModules = []) => {
        setCheckedMenu((prev) => {
            const newChecked = { ...prev };

            if (subModules.length > 0) {
                const newValue = !newChecked[id];
                newChecked[id] = newValue;
                subModules.forEach((sub) => (newChecked[sub.id] = newValue));
            } else {

                newChecked[id] = !newChecked[id];


                props.viewDetails.menuPermission.forEach((module) => {
                    if (module.sub_modules?.some((sub) => sub.id === id)) {
                        const allChecked = module.sub_modules.every((sub) => newChecked[sub.id]);
                        const noneChecked = module.sub_modules.every((sub) => !newChecked[sub.id]);

                        newChecked[module.id] = allChecked
                            ? true
                            : noneChecked
                                ? false
                                : newChecked[module.id];
                    }
                });
            }

            return newChecked;
        });
    };


    const handleCheck = (id, subModules = []) => {
        setChecked((prev) => {
            const newChecked = { ...prev };

            if (subModules.length > 0) {
                const newValue = !newChecked[id];
                newChecked[id] = newValue;
                subModules.forEach((sub) => (newChecked[sub.id] = newValue));
            } else {

                newChecked[id] = !newChecked[id];


                props.viewDetails.modulePermission.forEach((module) => {
                    if (module.sub_modules?.some((sub) => sub.id === id)) {
                        const allChecked = module.sub_modules.every((sub) => newChecked[sub.id]);
                        const noneChecked = module.sub_modules.every((sub) => !newChecked[sub.id]);

                        newChecked[module.id] = allChecked
                            ? true
                            : noneChecked
                                ? false
                                : newChecked[module.id];
                    }
                });
            }

            return newChecked;
        });
    };


    const isIndeterminate = (subModules) =>
        subModules.some((sub) => checked[sub.id]) &&
        !subModules.every((sub) => checked[sub.id]);

    const isIndeterminateMenu = (subModules) =>
        subModules.some((sub) => checkedMenu[sub.id]) &&
        !subModules.every((sub) => checkedMenu[sub.id]);


    const renderTreeItems = (modules, name) =>

        modules.map((module) => {
            const subModules = module.sub_modules || [];
            const isChecked = name === "menu" ? checkedMenu[module.id] : checked[module.id];
            const indeterminate = name === "menu" ? isIndeterminateMenu(subModules) : isIndeterminate(subModules);

            return (
                <CustomTreeItem
                    key={module.id}
                    itemId={module.id.toString()}
                    label={
                        <Box display="flex" alignItems="center">
                            <Checkbox
                                checked={isChecked}
                                indeterminate={indeterminate}
                                onChange={() => name === "menu" ? handleCheckMenu(module.id, subModules) : handleCheck(module.id, subModules)}
                            />
                            {module.name}
                        </Box>
                    }
                >
                    {subModules.length > 0 && renderTreeItems(subModules, name)}
                </CustomTreeItem>
            );
        });

    return (
        <div>
            <Dialog
                open={dialogDetails.open}
                onClose={handleClose}
                maxWidth="md"
                fullWidth
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <StyledHeaderTitle id="alert-dialog-title">
                    {props.viewDetails.id ? `Edit Role` : `Add Role`}
                </StyledHeaderTitle>
                <DialogContent>
                    <Box pt={3}>
                        <Grid container spacing={2}>
                            {/* Left Side */}
                            <Grid item xs={12} md={8}>
                                {/* {props.viewDetails.id ? null :
                                    <FlexContent>
                                        <FlexInnerTitle>
                                            <span>Company ID</span> <span> : </span>
                                        </FlexInnerTitle>
                                        <Values><TextField
                                            required
                                            label="Company ID"
                                            name="company_id"
                                            type="number"
                                            value={formData.company_id}
                                            onChange={handleChange}
                                            sx={{ marginBottom: '10px', width: "200px" }}

                                        /></Values>
                                    </FlexContent>
                                } */}

                                <FlexContent>
                                    <FlexInnerTitle>
                                        <span>Role Name</span> <span> : </span>
                                    </FlexInnerTitle>
                                    <Values><TextField
                                        required
                                        label="Role Name"
                                        type="text"
                                        name="name"
                                        value={formData.name}
                                        onChange={handleChange}
                                        sx={{ marginBottom: '10px', width: "200px" }}
                                    /></Values>
                                </FlexContent>
                            </Grid>
                            {/* {props.viewDetails.id ? null :
                                <Grid item xs={12} md={4}>
                                    <div style={{ display: 'flex', alignItems: 'center' }}>
                                        <span>Active Status</span>
                                        <Switch
                                            checked={formData.status === 1 ? true : false}
                                            onChange={handleSwitch}
                                            inputProps={{ 'aria-label': 'controlled' }}
                                        />
                                    </div>
                                </Grid>
                            } */}
                        </Grid>

                    </Box>
                    <Box pt={3}>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                                <h4>Menu Permission</h4>
                                <Box p={2} sx={{
                                    boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)"
                                }}>

                                    <SimpleTreeView
                                        defaultExpandedItems={['Dashboard']}
                                        slots={{
                                            expandIcon: AddBoxIcon,
                                            collapseIcon: IndeterminateCheckBoxIcon,
                                        }}
                                    >
                                        {renderTreeItems(props.viewDetails.menuPermission, "menu")}
                                    </SimpleTreeView>

                                </Box>
                            </Grid>

                            <Grid item xs={12} md={6}>
                                <h4>Module Permission</h4>
                                <Box p={2} sx={{
                                    boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)"
                                }}>

                                    <SimpleTreeView
                                        defaultExpandedItems={['Shopify', 'Erply']}
                                        slots={{
                                            expandIcon: AddBoxIcon,
                                            collapseIcon: IndeterminateCheckBoxIcon,

                                        }}
                                    >
                                        {renderTreeItems(props.viewDetails.modulePermission, "module")}
                                    </SimpleTreeView>

                                </Box>
                            </Grid>
                        </Grid>
                    </Box>
                </DialogContent>
                <DialogActions styled={{ margin: "5px 10px" }}>
                    <Button onClick={handleClose} color="error" variant="contained" autoFocus>
                        Cancel
                    </Button>
                    <Button
                        onClick={handleYes}
                        color="primary"
                        variant="contained"
                        autoFocus
                    >
                        {props.viewDetails.id ? `Edit` : `Save`}
                    </Button>

                </DialogActions>
            </Dialog>
        </div>
    );
};

export default EditDialogRole;