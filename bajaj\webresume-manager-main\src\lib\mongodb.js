import mongoose from "mongoose";

const MONGODB_URI = process.env.MONGODB_URI;
//console.log(MONGODB_URI);
// if (!MONGODB_URI) {
//   throw new Error(
//     "Please define the MONGODB_URI environment variable inside .env.local"
//   );
// }

// Global caching to avoid multiple connections during hot reloads in dev
let cached = global.mongoose;

if (!cached) {
  cached = global.mongoose = { conn: null, promise: null };
}

export async function connectToDB() {
  if (cached.conn) return cached.conn;
  if (!cached.promise) {
    cached.promise = mongoose
      .connect(MONGODB_URI, {
        dbName: "webresume",
        bufferCommands: false,
      })
      .then(() => console.log("Connected to DB"))
      .catch((err) => console.log(err));
  }

  cached.conn = await cached.promise;
  return cached.conn;
}
