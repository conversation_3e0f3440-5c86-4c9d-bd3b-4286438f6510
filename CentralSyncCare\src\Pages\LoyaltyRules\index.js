import React, { useEffect, useState } from "react";

import {
    Box,
    Button,
    Card,
    Collapse,
    FormControl,
    Grid,
    InputLabel,
    MenuItem,
    Select,
    styled,
    TextField,
    Snackbar,
} from "@mui/material";
import { Add, <PERSON><PERSON>or<PERSON>, Close, FilterList } from "@mui/icons-material";
import httpclient from "../../Utils";
import { useNavigate } from "react-router-dom";
import MuiAlert from "@mui/material/Alert";
import TableComponent from "../../Components/TableComponent";
import EditDialogRules from "../../Components/EditDialogRules";
import DeleteDialog from "../../Components/DeleteDialog";
import useTokenRefresh from "../../Hooks/useTokenRefresh";
import Footer from "../../Components/Footer";
import ViewLoyaltyRule from "./ViewLoyaltyRule";
//import ViewDialogRules from "./ViewDialogRules";
//import ViewMenuList from "./ViewMenuList";
// import DeactivateDialog from "../DeactivateDialog";
// import ResetDialog from "../ResetDialog";
// import DeleteDialog from "../DeleteDialog";

const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

const login = localStorage.getItem("login");
const loginData = JSON.parse(login);


const columns = [
    { id: "sn", name: "SN" },
    { id: "name", name: "Rule Name" },
    { id: "start_date", name: "Start Date" },
    { id: "end_date", name: "End Date" },
    { id: "statusRule", name: "Status" },
    { id: "template", name: "Template" },
    { id: "view", name: "View" },
    { id: "actions", name: "Actions" },
];


const superOptions = [
    { id: "edit", name: "Edit", action: "handleEdit" },
    { id: "deactivate", name: "Deactivate", action: "handleDeactivate" },
    { id: "reset", name: "Reset Password", action: "handleResetPassword" },
    { id: "delete", name: "Delete", action: "handleDelete" },
];

const adminOptions = [
    { id: "edit", name: "Edit", action: "handleEdit" },
    { id: "reset", name: "Reset Password", action: "handleResetPassword" },
]

const FilteredBox = styled(Box)(({ theme }) => ({
    background: "#f9f9f9",
    padding: "5px 10px",
    borderRadius: "5px",
    "& p": {
        margin: "0",
        marginRight: "10px",
        display: "inline-block",
        background: "#dedede",
        borderRadius: "10px",
        padding: "2px 5px",
    },
    "& svg": {
        fontSize: "15px",
        cursor: "pointer",
        position: "relative",
        top: "3px",
        background: theme.palette.primary.dark,
        color: "#fff",
        borderRadius: "50%",
        padding: "2px",
        marginLeft: "2px",
    },
}));

const Header = styled("div")(({ theme }) => ({
    "& h1": {
        color: theme.palette.primary.dark,
        margin: "0",
    },
}));

const AddButton = styled(Button)(({ theme }) => ({
    marginLeft: "10px",
    "& svg": {
        fontSize: "15px",
    },
}));

const configRowPerPage = JSON.parse(localStorage.getItem("configRowPerPage"));



const LoyaltyRules = (props) => {

    const { getTokenRefreshed: refresh, open: tokenOpen, overlay: overlay, setOpen: setTokenOpen, message: tokenMessage, messageState: tokenMessageState } = useTokenRefresh();

    const navigate = useNavigate();
    const [openResetDialog, setOpenResetDialog] = useState(false);
    const [view, setView] = useState(false);
    const [viewDetails, setViewDetails] = useState({});
    const [menuList, setMenuList] = useState([]);
    const [openActiveDialog, setOpenActiveDialog] = useState(false);
    const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
    const [openEditDialog, setOpenEditDialog] = useState(false);
    const [openViewDialog, setOpenViewDialog] = useState(false);
    const [openMenuListDialog, setOpenMenuListDialog] = useState(false);
    const [rows, setRows] = useState([]);
    const [modulePermissionList, setModulePermissionList] = useState([]);
    const [open, setOpen] = useState(false);
    const [message, setMessage] = useState("");
    const [messageState, setMessageState] = useState("");
    const [singleLoading, setSingleLoading] = useState(false);
    const [loading, setLoading] = useState(false);
    const [rowLoading, setRowLoading] = useState({});
    const [direction, setDirection] = useState(false);
    const [currentColumn, setCurrentColumn] = useState("");
    const [page, setPage] = useState(1);
    const [from, setFrom] = useState(1);
    const [to, setTo] = useState(
        configRowPerPage && configRowPerPage
            ? configRowPerPage && configRowPerPage
            : 20
    );

    const [rowsPerPage, setRowsPerPage] = useState(
        configRowPerPage && configRowPerPage
            ? configRowPerPage && configRowPerPage
            : 20
    );
    const [total, setTotal] = useState("");
    const [filterOpen, setFilterOpen] = useState(false);

    const [filterData, setFilterData] = useState({
        rule_id: "",
        rule_name: "",
        rule_status: "",
        startDate: "",
        endDate: "",
        remove: false,
    });

    const [submittedData, setSubmittedData] = useState({
        rule_id: "",
        rule_name: "",
        rule_status: "",
        startDate: "",
        endDate: "",
        submit: false,
    });

    // useEffect(() => {
    //   getAllLoyaltyRules();
    // }, []);

    useEffect(() => {
        if (
            filterData.rule_id === "" &&
            filterData.rule_name === "" &&
            filterData.rule_status === "" &&
            filterData.startDate === "" &&
            filterData.endDate === ""
        ) {
            setSubmittedData({
                ...submittedData,
                submit: false,
            });
        }
        if (filterData.rule_id === " ") filterData.rule_id = "";
        if (filterData.rule_name === " ") filterData.rule_name = "";
        if (filterData.rule_status === " ") filterData.rule_status = "";
        if (filterData.startDate === " ") filterData.startDate = "";
        if (filterData.endDate === " ") filterData.endDate = "";

        filterData.remove === true && handleFilter();
    }, [filterData]);

    useEffect(() => {
        let userStorage = JSON.parse(localStorage.getItem("rules_filter"));
        userStorage !== null && setFilterData(userStorage);

        userStorage == null
            ? getAllLoyaltyRules()
            : userStorage.rule_id == "" &&
                userStorage.rule_name == "" &&
                userStorage.rule_status == "" &&
                userStorage.startDate == "" &&
                userStorage.endDate == "" &&


                userStorage.removed == false
                ? getAllLoyaltyRules()
                : console.log("users!");
    }, []);

    const getAllLoyaltyRules = () => {
        setLoading(true);
        httpclient.get(`request-response?requestName=lightspeed/loyalty-rules&pagination=${rowsPerPage}`).then(({ data }) => {
            if (data.success) {
                setRows(data.data);
                setModulePermissionList(data.modulePermissionList);
                setTotal(data.meta.total);
                setRowsPerPage(parseInt(data.meta.per_page));
                setPage(data.meta.current_page);
                setFrom(data.meta.from);
                setTo(data.meta.to);
                setLoading(false);
            } else {
                setOpen(true);
                setMessage(data.message);
                setMessageState("error");
                setLoading(false);
            }

        }).catch((err) => {
            if (err.response.status === 401) {
                refresh();
                setOpen(tokenOpen);
                setMessage(tokenMessage);
                setMessageState("error");
            } else if (err.response.status === 422) {
                const errorMessages = Object.values(err.response.data.errors).flat();
                setOpen(true);
                setMessage(errorMessages);
                setMessageState("error");
                setLoading(false);
            } else if (err.response.status === 400) {
                const errorMessages = Object.values(err.response.data.errors).flat();
                setOpen(true);
                setMessage(errorMessages);
                setMessageState("error");
                setLoading(false);

            } else {
                setOpen(true);
                setMessage(err.response.data.message);
                setMessageState("error");
                setLoading(false);
            }
        })

    };

    const hadleFilterOpen = () => {
        setFilterOpen((prev) => !prev);
    };

    const handleChangeFilter = (e) => {
        const { name, value } = e.target;
        setFilterData({
            ...filterData,
            [name]: value,
            remove: false,
        });
    };

    const handleFilter = () => {
        setSubmittedData({
            ...submittedData,
            rule_id: filterData.rule_id,
            rule_name: filterData.rule_name,
            rule_status: filterData.rule_status,
            startDate: filterData.startDate,
            endDate: filterData.endDate,

            submit: true,
        });
        filterData.remove = true;
        localStorage.setItem("rules_filter", JSON.stringify(filterData));
        setLoading(true);
        if (
            filterData.rule_id ||
            filterData.rule_name ||
            filterData.rule_status ||
            filterData.startDate ||
            filterData.endDate
        ) {
            httpclient
                .get(
                    `request-response?requestName=lightspeed/loyalty-rules&filters[rule_id][$eq]=${filterData.rule_id}&filters[name][$contains]=${filterData.rule_name
                    }&filters[is_active][$eq]=${filterData.rule_status
                    }&filters[start_date][$between][0]=${filterData.startDate
                    }&filters[end_date][$between][1]=${filterData.endDate
                    }&pagination=${rowsPerPage}&page=${1}`
                )
                .then(({ data }) => {
                    if (data.success) {
                        setRows(data.data);
                        setModulePermissionList(data.modulePermissionList);
                        setTotal(data.meta.total);
                        setRowsPerPage(data.meta.per_page);
                        setPage(data.meta.current_page);
                        setFrom(data.meta.from);
                        setTo(data.meta.to);
                        setLoading(false);
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })

        } else {
            getAllLoyaltyRules();
        }
    };

    const handleRemove = (data) => {
        if (data === "startDate") {
            setFilterData({
                ...filterData,
                startDate: "",
                endDate: "",
                remove: true,
            });
            setSubmittedData({
                ...submittedData,
                startDate: "",
                endDate: "",
            });
        } else {
            setFilterData({
                ...filterData,
                [data]: "",
                remove: true,
            });

            setSubmittedData({
                ...submittedData,
                [data]: "",
            });
        }
    };

    const handleSort = (column) => {
        setDirection((prevDirection) => !prevDirection);
        setCurrentColumn(column);
        setLoading(true);
        submittedData.submit
            ? httpclient
                .get(
                    `request-response?requestName=lightspeed/loyalty-rules&filters[rule_id][$eq]=${filterData.rule_id
                    }&filters[name][$contains]=${filterData.rule_name
                    }&filters[is_active][$eq]=${filterData.rule_status
                    }&filters[start_date][$between][0]=${filterData.startDate
                    }&filters[end_date][$between][1]=${filterData.endDate
                    }&sort[0]=${column}:${!direction ? "asc" : "desc"
                    }&pagination=${rowsPerPage}&page=${page}`
                )
                .then(({ data }) => {
                    if (data.success) {
                        setRows(data.data);
                        setTotal(data.meta.total);
                        setRowsPerPage(parseInt(data.meta.per_page));
                        setPage(data.meta.current_page);
                        setFrom(data.meta.from);
                        setTo(data.meta.to);
                        setLoading(false);
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })
            : httpclient
                .get(
                    `request-response?requestName=lightspeed/loyalty-rules&sort[0]=${column}:${!direction ? "asc" : "desc"
                    }&pagination=${rowsPerPage}`
                )
                .then(({ data }) => {
                    if (data.success) {
                        setRows(data.data);
                        setTotal(data.meta.total);
                        setRowsPerPage(parseInt(data.meta.per_page));
                        setPage(data.meta.current_page);
                        setFrom(data.meta.from);
                        setTo(data.meta.to);
                        setLoading(false);
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })
    };

    const handleChangePage = (e, page) => {
        setLoading(true);
        submittedData.submit
            ? httpclient
                .get(
                    `request-response?requestName=lightspeed/loyalty-rules&filters[rule_id][$eq]=${filterData.rule_id
                    }&filters[name][$contains]=${filterData.rule_name
                    }&filters[is_active][$eq]=${filterData.rule_status
                    }&filters[start_date][$between][0]=${filterData.startDate
                    }&filters[end_date][$between][1]=${filterData.endDate
                    }${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''
                    }&pagination=${rowsPerPage}&page=${page}`
                )
                .then(({ data }) => {
                    if (data.success) {
                        setRows(data.data);
                        setTotal(data.meta.total);
                        setRowsPerPage(parseInt(data.meta.per_page));
                        setPage(data.meta.current_page);
                        setFrom(data.meta.from);
                        setTo(data.meta.to);
                        setLoading(false);
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })
            : httpclient
                .get(
                    `request-response?requestName=lightspeed/loyalty-rules&pagination=${rowsPerPage}&page=${page}`
                )
                .then(({ data }) => {
                    if (data.success) {
                        setRows(data.data);
                        setTotal(data.meta.total);
                        setRowsPerPage(parseInt(data.meta.per_page));
                        setPage(data.meta.current_page);
                        setFrom(data.meta.from);
                        setTo(data.meta.to);
                        setLoading(false);
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(+event.target.value);
        setLoading(true);

        localStorage.setItem("configRowPerPage", event.target.value);

        submittedData.submit
            ? httpclient
                .get(
                    `request-response?requestName=lightspeed/loyalty-rules&filters[rule_id][$eq]=${filterData.rule_id
                    }&filters[name][$contains]=${filterData.rule_name
                    }&filters[is_active][$eq]=${filterData.rule_status
                    }&filters[start_date][$between][0]=${filterData.startDate
                    }&filters[end_date][$between][1]=${filterData.endDate
                    }${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''
                    }&pagination=${+event.target.value}&page=${page}`
                )
                .then(({ data }) => {
                    setLoading(true);
                    if (data.success) {
                        setRows(data.data);
                        setTotal(data.meta.total);
                        setRowsPerPage(parseInt(data.meta.per_page));
                        setPage(data.meta.current_page);
                        setFrom(data.meta.from);
                        setTo(data.meta.to);
                        setLoading(false);
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })
            : httpclient
                .get(
                    `request-response?requestName=lightspeed/loyalty-rules&pagination=${+event
                        .target.value}&page=${1}`
                )
                .then(({ data }) => {
                    setLoading(true);
                    if (data.success) {
                        setRows(data.data);
                        setTotal(data.meta.total);
                        setRowsPerPage(parseInt(data.meta.per_page));
                        setFrom(data.meta.from);
                        setTo(data.meta.to);
                        setPage(data.meta.current_page);
                        setLoading(false);
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })
    };


    const handleAddNew = () => {
        //   const updatedModulePermissionList = modulePermissionList.map((module) => ({
        //     id: module.module_id,
        //     name: module.module_name,
        //     sub_modules: module.sub_modules,
        //   }));
        //   setViewDetails({
        //     modulePermission: [...updatedModulePermissionList]
        //   });
        setOpenEditDialog(true)
    };

    const handleEdit = (row) => {
        setOpenEditDialog(true)
        setViewDetails(row);
    };

    const handleCloseLoyaltyRule = () => {
        setOpenViewDialog(false);
        setViewDetails({});
    };

    const handleView = (row) => {
        setSingleLoading(true);
        setOpenViewDialog(true);
        httpclient
            .get(`request-response?requestName=lightspeed/loyalty-rules/${row.id || row}`)
            .then(({ data }) => {
                if (data.status === 200) {
                    setViewDetails(data.data);
                    setSingleLoading(false);
                }
                else {
                    setOpen(true);
                    setMessage(data.message);
                    setMessageState("error");
                    setSingleLoading(false);
                }

            }).catch((err) => {
                if (err.response.status === 401) {
                    refresh();
                    setOpen(tokenOpen);
                    setMessage(tokenMessage);
                    setMessageState("error");
                } else if (err.response.status === 422) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setSingleLoading(false);
                } else if (err.response.status === 400) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setSingleLoading(false);

                } else {
                    setOpen(true);
                    setMessage(err.response.data.message);
                    setMessageState("error");
                    setSingleLoading(false);
                }
            })

    };

    const handleViewMenuList = (row) => {
        setOpenMenuListDialog(true)
        setMenuList(row.menus);
    };
    const handleCloseMenuList = () => {
        setOpenMenuListDialog(false)
        setMenuList([]);
    };
    const handleCloseView = () => {
        setOpenViewDialog(false)
        setViewDetails({});
    };

    const sendEdit = (call, formData) => {
        if (call.open === false) {

            setOpenEditDialog(false);
            setViewDetails({});
            setView(false);
        }
        if (call.success === true) {
            viewDetails.id ? (
                httpclient
                    .put(`request-response?requestName=lightspeed/loyalty-rules/${viewDetails.id}`, formData)
                    .then(({ data }) => {
                        if (data.status === 200) {
                            setOpen(true);
                            setMessageState("success");
                            setMessage(data.message);
                            setOpenActiveDialog(false);
                            setViewDetails({});
                            getAllLoyaltyRules();
                        }
                        else {
                            setOpen(true);
                            setMessage(data.message);
                            setMessageState("error");
                            setLoading(false);
                        }

                    }).catch((err) => {
                        if (err.response.status === 401) {
                            refresh();
                            setOpen(tokenOpen);
                            setMessage(tokenMessage);
                            setMessageState("error");
                        } else if (err.response.status === 422) {
                            const errorMessages = Object.values(err.response.data.errors).flat();
                            setOpen(true);
                            setMessage(errorMessages);
                            setMessageState("error");
                            setLoading(false);
                        } else if (err.response.status === 400) {
                            const errorMessages = Object.values(err.response.data.errors).flat();
                            setOpen(true);
                            setMessage(errorMessages);
                            setMessageState("error");
                            setLoading(false);

                        } else {
                            setOpen(true);
                            setMessage(err.response.data.message);
                            setMessageState("error");
                            setLoading(false);
                        }
                    })
            ) :
                httpclient
                    .post(`request-response?requestName=lightspeed/loyalty-rules&checkError=1`, formData)
                    .then(({ data }) => {
                        if (data.status === 200) {
                            setOpen(true);
                            setMessageState("success");
                            setMessage(data.message);
                            setOpenActiveDialog(false);
                            setViewDetails({});
                            getAllLoyaltyRules();
                        } else {
                            setOpen(true);
                            setMessage(data.message);
                            setMessageState("error");
                            setLoading(false);
                        }

                    }).catch((err) => {
                        if (err.response.status === 401) {
                            refresh();
                            setOpen(tokenOpen);
                            setMessage(tokenMessage);
                            setMessageState("error");
                        } else if (err.response.status === 422) {
                            const errorMessages = Object.values(err.response.data.errors).flat();
                            setOpen(true);
                            setMessage(errorMessages);
                            setMessageState("error");
                            setLoading(false);
                        } else if (err.response.status === 400) {
                            const errorMessages = Object.values(err.response.data.errors).flat();
                            setOpen(true);
                            setMessage(errorMessages);
                            setMessageState("error");
                            setLoading(false);

                        } else {
                            setOpen(true);
                            setMessage(err.response.data.message);
                            setMessageState("error");
                            setLoading(false);
                        }

                    })
        }
    };


    const handleDelete = (row) => {
        setOpenDeleteDialog(true);
        setViewDetails(row)
    };

    const sendDelete = (call) => {
        if (call.open === false) {
            setOpenDeleteDialog(false)
            setViewDetails({})
        }
        if (call.success === true) {
            httpclient
                .delete(`request-response?requestName=lightspeed/loyalty-rules/${viewDetails.id}`)
                .then(({ data }) => {
                    if (data.status === 200) {
                        setOpen(true);
                        setMessageState("success");
                        setMessage(data.message);
                        setOpenDeleteDialog(false);
                        setViewDetails({});
                        getAllLoyaltyRules();
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })
        }
    }

    // Handle API call to update row status
    const updateRowStatus = (rowId) => {
        setRowLoading((prev) => ({ ...prev, [rowId.id]: true })); 
        httpclient
            .post(`request-response?requestName=lightspeed/loyalty-rules/status/${rowId.id}`)
            .then(({ data }) => {
                if (data.status === 200) {
                    setRowLoading((prev) => ({ ...prev, [rowId.id]: false }));
                    setOpen(true);
                    setMessageState("success");
                    setMessage(data.message);
                    setViewDetails({});
                    getAllLoyaltyRules();
                } else {
                    setRowLoading((prev) => ({ ...prev, [rowId.id]: false }));
                    setOpen(true);
                    setMessageState("error");
                    setMessage(data.error || data.message);
                }
            }).catch((err) => {
                if (err?.response?.status === 401) {
                    refresh();
                    setOpen(tokenOpen);
                    setMessage(tokenMessage);
                    setMessageState("error");
                } else if (err?.response?.status === 422) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setRowLoading((prev) => ({ ...prev, [rowId.id]: false }));
                } else if (err?.response?.status === 400) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setRowLoading((prev) => ({ ...prev, [rowId.id]: false }));

                } else {
                    setOpen(true);
                    setMessage(err?.response?.data?.message);
                    setMessageState("error");
                    setRowLoading((prev) => ({ ...prev, [rowId.id]: false }));
                }
            }).finally(() => {
                setRowLoading((prev) => ({ ...prev, [rowId.id]: false }));
            });
    };


    const currentChange = (value, row) => {

        if (value === "allow_update") {
            handleEdit(row);
        }

        if (value === "allow_delete") {
            handleDelete(row);
        }
    };

    const handleClose = (event, reason) => {
        if (reason === "clickaway") {
            return;
        }
        setOpen(false);
        setTokenOpen(false);
    };

    return (
        <div>
            <Grid container spacing={2}>
                <Grid item md={8} xs={12}>
                    <Header>
                        <h1>List Loyalty Rules</h1>
                    </Header>
                </Grid>
                <Grid
                    item
                    md={4}
                    xs={12}
                    display="flex"
                    alignItems="center"
                    justifyContent="flex-end"
                >
                    <Button color="primary" variant="contained" onClick={hadleFilterOpen}>
                        Filter <FilterList style={{ marginLeft: "5px" }} fontSize="small" />
                    </Button>

                    {props?.permissions?.some((pre) => pre.name === "allow_create" && pre.status === 1) &&
                        <AddButton
                            color="primary"
                            variant="contained"
                            onClick={handleAddNew}
                        >
                            <Add style={{ marginRight: "5px" }} fontSize="small" /> Add Rule
                        </AddButton>
                    }
                </Grid>

                {/* Filter */}
                <Grid item xs={12}>
                    <Collapse in={filterOpen}>
                        <Card>
                            <Box p={4}>
                                <Grid container spacing={2}>
                                    {/* <Grid item xs={12} md={4}>
                                        <InputLabel>Project ID</InputLabel>
                                        <TextField
                                            variant="outlined"
                                            name="rule_id"
                                            value={filterData.rule_id}
                                            onChange={handleChangeFilter}
                                            onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                                            fullWidth
                                        />
                                    </Grid> */}
                                    <Grid item xs={12} md={4}>
                                        <InputLabel>Rule Name</InputLabel>
                                        <TextField
                                            variant="outlined"
                                            name="rule_name"
                                            value={filterData.rule_name}
                                            onChange={handleChangeFilter}
                                            onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                                            fullWidth
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={4}>
                                        <InputLabel>Status</InputLabel>
                                        {/* <TextField
                                            variant="outlined"
                                            name="rule_status"
                                            value={filterData.rule_status}
                                            onChange={handleChangeFilter}
                                            onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                                            fullWidth
                                        /> */}

                                        <FormControl fullWidth>
                                            <Select
                                                name="rule_status"
                                                value={filterData.rule_status}
                                                onChange={handleChangeFilter}
                                                onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                                            >
                                                <MenuItem value={""}>Select</MenuItem>
                                                <MenuItem value={"1"}>Active</MenuItem>
                                                <MenuItem value={"0"}>Inactive</MenuItem>

                                            </Select>
                                        </FormControl>



                                    </Grid>
                                    <Grid item xs={12} md={4}>
                                        <InputLabel>Start Date</InputLabel>
                                        <TextField
                                            variant="outlined"
                                            name="startDate"
                                            type="date"
                                            value={filterData.startDate}
                                            onChange={(e) => handleChangeFilter(e)}
                                            fullWidth
                                            InputLabelProps={{
                                                shrink: true,
                                            }}
                                        />
                                    </Grid>

                                    <Grid item xs={12} md={4}>
                                        <InputLabel>End Date</InputLabel>
                                        <TextField
                                            variant="outlined"
                                            name="endDate"
                                            type="date"
                                            value={filterData.endDate}
                                            onChange={(e) => handleChangeFilter(e)}
                                            fullWidth
                                            InputLabelProps={{
                                                shrink: true,
                                            }}
                                        />
                                    </Grid>



                                    <Grid item xs={12}>
                                        <Box textAlign={"right"}>
                                            <Button
                                                variant="contained"
                                                color="primary"
                                                onClick={handleFilter}
                                            >
                                                Filter{" "}
                                                <ArrowForward
                                                    fontSize="small"
                                                    style={{ marginLeft: "5px" }}
                                                />
                                            </Button>
                                        </Box>
                                    </Grid>
                                </Grid>
                            </Box>
                        </Card>
                    </Collapse>
                </Grid>

                {submittedData.rule_id ||
                    submittedData.rule_name ||
                    submittedData.rule_status ||
                    submittedData.startDate ||
                    submittedData.endDate ? (
                    <Grid item xs={12}>
                        <FilteredBox>
                            <span>Filtered: </span>
                            {submittedData.rule_id && (
                                <p>
                                    <span>Rule ID: {submittedData.rule_id}</span>
                                    <Close
                                        fontSize="small"
                                        onClick={() => handleRemove("rule_id")}
                                    />
                                </p>
                            )}
                            {submittedData.rule_name && (
                                <p>
                                    <span>Rule Name: {submittedData.rule_name}</span>
                                    <Close
                                        fontSize="small"
                                        onClick={() => handleRemove("rule_name")}
                                    />
                                </p>
                            )}
                            {submittedData.rule_status && (
                                <p>
                                    <span>Status: {submittedData.rule_status === "1" ? "Active" : "Inactive"}</span>
                                    <Close
                                        fontSize="small"
                                        onClick={() => handleRemove("rule_status")}
                                    />
                                </p>
                            )}
                            {(submittedData.startDate || submittedData.endDate) && (
                                <p>
                                    <span>
                                        Date Range: {submittedData.startDate} -{" "}
                                        {submittedData.endDate}
                                    </span>
                                    <Close
                                        fontSize="small"
                                        onClick={() => handleRemove("startDate")}
                                    />
                                </p>
                            )}

                        </FilteredBox>
                    </Grid>
                ) : (
                    <Box></Box>
                )}
                {/* Filter */}

                <Grid item xs={12}>
                    <TableComponent
                        name={"Rule"}
                        columns={columns}
                        rows={rows}
                        sort={true}
                        handleViewMenuList={handleViewMenuList}
                        handleSort={handleSort}
                        handleNewView={handleView}
                        updateRowStatus={updateRowStatus}
                        setRowLoading={setRowLoading}
                        rowLoading={rowLoading}
                        props={props}
                        options={props?.permissions}
                        currentChange={currentChange}
                        loading={loading}
                        direction={direction}
                        currentColumn={currentColumn}
                        handleChangeRowsPerPage={handleChangeRowsPerPage}
                        handleChangePage={handleChangePage}
                        page={page}
                        total={total && total}
                        fromTable={from}
                        toTable={to}
                        rowsPerPage={rowsPerPage}
                    />
                </Grid>
                <Footer overlay={overlay || props.overlayNew} />
            </Grid>



            {openDeleteDialog && <DeleteDialog name={"Loyalty Rules"} viewDetails={viewDetails} sendDelete={sendDelete} />}

            {openEditDialog && (
                <EditDialogRules
                    viewDetails={viewDetails}
                    sendEdit={sendEdit}
                    view={view}
                />
            )}

            {openViewDialog && (
                <ViewLoyaltyRule
                    singleLoading={singleLoading}
                    viewDetails={viewDetails}
                    handleCloseLoyaltyRule={handleCloseLoyaltyRule}
                />
            )}

            <Snackbar
                autoHideDuration={3000}
                anchorOrigin={{ vertical: "top", horizontal: "right" }}
                open={open || tokenOpen}
                onClose={handleClose}
            >
                <Alert
                    onClose={handleClose}
                    severity={messageState || tokenMessageState}
                    sx={{ width: "100%" }}
                >
                    {message || tokenMessage}
                </Alert>
            </Snackbar>
        </div>
    );
};

export default LoyaltyRules;
