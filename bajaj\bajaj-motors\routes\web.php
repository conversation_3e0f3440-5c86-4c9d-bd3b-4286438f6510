<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\BikeController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Home page
Route::get('/', [HomeController::class, 'index'])->name('home');

// About page
Route::get('/about', function () {
    return view('about');
})->name('about');

// Blog routes
Route::prefix('blogs')->name('blogs.')->group(function () {
    Route::get('/', [BlogController::class, 'index'])->name('index');
    Route::get('/{slug}', [BlogController::class, 'show'])->name('show');
});

// Bike routes
Route::prefix('bikes')->name('bikes.')->group(function () {
    Route::get('/{slug}', [BikeController::class, 'show'])->name('show');
});

// API routes for AJAX requests
Route::prefix('api')->name('api.')->group(function () {
    // Bikes data
    Route::get('/bikes', [HomeController::class, 'getBikesData'])->name('bikes.data');
    Route::get('/bikes/{id}', [BikeController::class, 'getBikeData'])->name('bikes.single');
    Route::get('/bikes/brand/{brand}', [BikeController::class, 'getByBrand'])->name('bikes.brand');
    
    // Blogs data
    Route::get('/blogs', [BlogController::class, 'getBlogsData'])->name('blogs.data');
});

// Additional static pages (you can add controllers for these later)
Route::get('/showrooms', function () {
    return view('showrooms');
})->name('showrooms');

Route::get('/workshops', function () {
    return view('workshops');
})->name('workshops');

Route::get('/events', function () {
    return view('events');
})->name('events');

Route::get('/book-test-ride', function () {
    return view('book-test-ride');
})->name('book-test-ride');

Route::get('/news', function () {
    return view('news');
})->name('news');

Route::get('/contact', function () {
    return view('contact');
})->name('contact');

Route::get('/faqs', function () {
    return view('faqs');
})->name('faqs');
