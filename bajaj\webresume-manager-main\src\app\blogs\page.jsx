"use client";
import Image from "next/image";
import { useEffect, useState } from "react";
import { FilePenLine, Trash2 } from "lucide-react";
import Link from "next/link";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { deleteBlog } from "@/actions/blog";
import { toast } from "react-toastify";

const page = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [blogs, setBlogs] = useState([]);

  //check authentication
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/");
    }
  }, [status, router]);

  //fetch blogs
  useEffect(() => {
    const fetchBlogs = async () => {
      if (!session?.user?.userId) return;

      try {
        const res = await fetch(`/api/blog/${session.user.userId}`);
        const data = await res.json();
        setBlogs(data);
      } catch (err) {
        console.error("Error fetching blogs:", err);
      }
      //console.log(blogs.image);
    };

    fetchBlogs();
  }, [session, status]);

  //handle delete blog
  const handleDelete = async (blogId) => {
    try {
      await deleteBlog(blogId);
      setBlogs((prevBlogs) => prevBlogs.filter((b) => b._id !== blogId));
      toast.success("Blog deleted successfully!");
    } catch (error) {
      console.log("Error in handle delete", error);
    }
  };

  //handle edit blog
  const handleEdit = (blogId) => {
    router.push(`/blogs/editblog/?id=${blogId}`);
  };

  if (status === "loading") {
    return <p>Loading...</p>;
  }

  return (
    <div className="px-4 md:px-8 xl:px-16">
      <div className="flex items-center gap-16 mb-12">
        <h1 className="text-3xl font-semibold">Blog Management</h1>
        <Link href={"/blogs/addblog"}>
          <button className="bg-gray-800 rounded-xl text-white px-4 py-2 hover:bg-gray-900 cursor-pointer">
            Add Blog
          </button>
        </Link>
      </div>

      <div className="flex gap-6 flex-wrap">
        {blogs.length ? (
          blogs.map((blog) => (
            <div
              key={blog._id}
              className="bg-gray-100 border shadow-lg w-72 rounded-xl"
            >
              <Image
                src={blog.image || "/no-image.png"}
                width={240}
                height={240}
                alt="blog image"
                className="rounded-t-xl h-60 w-full object-cover"
              />
              <div>
                <h2 className="text-xl font-semibold py-2 px-2">
                  {blog.title}
                </h2>
                <p className="text-sm px-2 text-gray-600">{blog.desc}</p>
                {/* <p className="p-2 text-gray-600">{Date.now().toString()}</p> */}
                <div className="flex items-center justify-end gap-2 px-2  pb-4">
                  <Button
                    onClick={() => handleEdit(blog._id)}
                    className="bg-gray-200 hover:bg-gray-300 cursor-pointer"
                  >
                    <FilePenLine />
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={() => handleDelete(blog._id)}
                    className="bg-gray-200 hover:bg-gray-300 cursor-pointer"
                  >
                    <Trash2 className="text-red-500" />
                  </Button>
                </div>
              </div>
            </div>
          ))
        ) : (
          <p>No Blogs Found....</p>
        )}
      </div>
    </div>
  );
};

export default page;
