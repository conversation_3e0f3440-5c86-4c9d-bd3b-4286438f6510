<div class="bg-transparent py-2 font-roboto lg:px-[150px] px-4">
    <nav class="floating-navbar bg-white my-4 px-6">
        <!-- Mobile Navigation -->
        <div class="lg:hidden flex items-center justify-between py-4">
            <!-- Mobile Menu Button -->
            <button class="flex items-center justify-center w-8 h-8" id="mobile-menu-btn">
                <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
            </button>

            <!-- Mobile Logo -->
            <img class="h-12" src="{{ asset('assets/images/logo.png') }}" alt="logo" />

            <!-- Mobile BIKES Button -->
            <button class="text-sm font-medium text-gray-700 flex items-center space-x-1" id="mobile-bikes-btn">
                <span>BIKES</span>
                <svg class="w-4 h-4 transition-transform duration-200" id="mobile-bikes-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </button>
        </div>

        <!-- Desktop Navigation -->
        <div class="hidden lg:flex justify-evenly items-center py-4 text-base">
            <!-- Left Navigation Items -->
            <div class="flex items-center space-x-8">
                <!-- Motorcycles Dropdown -->
                <div class="relative dropdown">
                    <button
                        class="text-sm flex items-center space-x-1 text-gray-700 hover:text-blue-600 transition-colors duration-200"
                        onclick="toggleDropdown('motorcycles')">
                        <span>MOTORCYCLES</span>
                        <svg class="w-4 h-4 transition-transform duration-200" id="motorcycles-arrow" fill="none"
                            stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>

                    <!-- Dropdown Content -->
                    <div id="motorcycles-dropdown"
                        class="dropdown-content absolute top-full left-0 mt-12 bg-white border border-gray-200 z-50 overflow-hidden"
                        style="width: 1000px; height: 600px;">
                        <div class="flex h-full">
                            <!-- Left Sidebar - Brands -->
                            <div class="w-64 bg-gray-50 p-6 rounded-l-lg flex-shrink-0">
                                <h3 class="text-gray-800 font-semibold mb-4">BRANDS</h3>
                                <ul class="space-y-2" id="brand-list">
                                    <!-- Brands will be populated by JavaScript -->
                                </ul>
                            </div>

                            <!-- Right Content - Motorcycles -->
                            <div class="flex-1 flex flex-col h-full">
                                <!-- Category Filter -->
                                <div class="flex space-x-4 p-6 pb-4 flex-shrink-0 border-b border-gray-100">
                                    <button
                                        class="category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 active"
                                        onclick="filterCategory('All')">
                                        All
                                    </button>
                                    <button
                                        class="category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-200"
                                        onclick="filterCategory('Classic')">
                                        Classic
                                    </button>
                                    <button
                                        class="category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-200"
                                        onclick="filterCategory('NS')">
                                        NS
                                    </button>
                                    <button
                                        class="category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-200"
                                        onclick="filterCategory('N')">
                                        N
                                    </button>
                                    <button
                                        class="category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-200"
                                        onclick="filterCategory('Adventure')">
                                        Adventure
                                    </button>
                                    <button
                                        class="category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-200"
                                        onclick="filterCategory('Cruiser')">
                                        Cruiser
                                    </button>
                                    <button
                                        class="category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-200"
                                        onclick="filterCategory('Commuter')">
                                        Commuter
                                    </button>
                                    <button
                                        class="category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-200"
                                        onclick="filterCategory('Economy')">
                                        Economy
                                    </button>
                                </div>

                                <!-- Motorcycle Grid with Scrolling -->
                                <div id="motorcycle-grid" class="flex-1 overflow-y-auto px-6 py-4">
                                    <!-- Motorcycles will be populated by JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <a href="#" class="text-gray-700 text-sm hover:text-blue-600 transition-colors duration-200">SHOWROOMS</a>
                <a href="#" class="text-gray-700 text-sm hover:text-blue-600 transition-colors duration-200">WORKSHOPS</a>
                <a href="#" class="text-gray-700 text-sm hover:text-blue-600 transition-colors duration-200">EVENTS</a>
            </div>

            <!-- Center Logo -->
            <img class="h-[72px] px-4" src="{{ asset('assets/images/logo.png') }}" alt="logo" />

            <!-- Right Navigation Items -->
            <div class="flex text-sm items-center space-x-8">
                <a href="#" class="text-gray-700 hover:text-blue-600 transition-colors duration-200">BOOK TEST RIDE</a>
                <a href="/about" class="text-gray-700 hover:text-blue-600 transition-colors duration-200">ABOUT US</a>
                <a href="#" class="text-gray-700 hover:text-blue-600 transition-colors duration-200">NEWS</a>

                <!-- Media Center Dropdown -->
                <div class="relative dropdown">
                    <button
                        class="flex items-center space-x-1 text-gray-700 hover:text-blue-600 transition-colors duration-200"
                        onclick="toggleDropdown('media')">
                        <span>MEDIA CENTER</span>
                        <svg class="w-4 h-4 transition-transform duration-200" id="media-arrow" fill="none"
                            stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>

                    <!-- Media Dropdown Content -->
                    <div id="media-dropdown"
                        class="media-dropdown-content absolute top-full right-0 mt-12 bg-white border border-gray-200 z-50 hidden"
                        style="width: 220px">
                        <div class="py-2">
                            <a href="/about"
                                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">ABOUT
                                US</a>
                            <a href="#"
                                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">ANNOUNCEMENTS</a>
                            <a href="#"
                                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">EVENTS</a>
                            <a href="/blogs"
                                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">BLOGS</a>
                            <a href="#"
                                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">DOWNLOAD
                                CENTER</a>
                            <a href="#"
                                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">CONTACT
                                US</a>
                            <a href="#"
                                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">FAQS</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Mobile Bikes Full-Screen Menu -->
    <div id="mobile-bikes-dropdown" class="lg:hidden">
        <!-- Header -->
        <div class="flex items-center justify-between p-4 border-b border-gray-200">
            <span class="font-semibold text-lg">BIKES</span>
            <button id="close-mobile-bikes" class="text-gray-500 hover:text-gray-700">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <!-- Brands and Categories for mobile can be rendered here similarly -->
    </div>
</div> 