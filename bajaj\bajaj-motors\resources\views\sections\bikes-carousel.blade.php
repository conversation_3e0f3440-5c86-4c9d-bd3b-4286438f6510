<!-- Bikes Carousel section -->
<section class="w-full bg-white relative overflow-hidden">
    <!-- Background Text -->
    <div class="absolute inset-0 flex items-center justify-center pointer-events-none">
        <h1 id="background-brand-text" class="text-[20vw] lg:text-[25vw] font-black text-indigo-100 select-none transition-all duration-500">
            PULSAR
        </h1>
    </div>

    <!-- Brand Navigation Tabs - Top -->
    <div class="relative z-10 pt-8 pb-4">
        <nav class="flex justify-center space-x-8 lg:space-x-16">
            @php
                $brands = ['PULSAR', 'DOMINAR', 'AVENGERS', 'DISCOVER', 'PLATINA'];
            @endphp
            @foreach($brands as $index => $brand)
                <button class="s1-tab text-lg lg:text-xl font-semibold {{ $index === 0 ? 'text-gray-800 border-black' : 'text-gray-400 border-transparent' }} border-b-2 pb-2 transition-all duration-300 focus:outline-none hover:text-gray-600" data-brand="{{ $brand }}">
                    {{ $brand }}
                </button>
            @endforeach
        </nav>
    </div>

    <!-- Main Content Area -->
    <div class="relative z-10 max-w-7xl mx-auto px-4 py-8">
        <!-- Bike Title and Description -->
        <div class="text-center mb-8">
            <h2 id="s1-title" class="text-3xl lg:text-5xl font-bold text-gray-900 mb-4 transition-all duration-300">
                @if(isset($featuredBikes) && count($featuredBikes) > 0)
                    {{ $featuredBikes->first()->name }}
                @else
                    PULSAR 220F ABS
                @endif
            </h2>
            <p id="s1-description" class="text-gray-600 text-base lg:text-lg max-w-3xl mx-auto leading-relaxed">
                @if(isset($featuredBikes) && count($featuredBikes) > 0)
                    {{ $featuredBikes->first()->description }}
                @else
                    Powerful High Performance bike with advanced technology
                @endif
            </p>
        </div>

        <!-- Main Bike Display Grid -->
        <div class="grid grid-cols-12 gap-4 lg:gap-8 items-center min-h-[500px]">
            <!-- Left Side - Navigation Arrow Only -->
            <div class="col-span-2 flex justify-center items-center">
                <button id="s1-prevBtn" class="w-12 h-12 lg:w-14 lg:h-14 rounded-full border border-gray-300 flex items-center justify-center hover:border-gray-500 hover:bg-gray-50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-300" aria-label="Previous model" title="Previous Model">
                    <svg class="w-6 h-6 lg:w-7 lg:h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>
            </div>

            <!-- Center - Main Bike Image -->
            <div class="col-span-8 flex justify-center items-center">
                <div class="relative w-full max-w-4xl">
                    <img id="s1-bikeImage" 
                         src="@if(isset($featuredBikes) && count($featuredBikes) > 0){{ $featuredBikes->first()->hero_image ?? asset('assets/bikes/pulsar/pulsar_220f_abs.png') }}@else{{ asset('assets/bikes/pulsar/pulsar_220f_abs.png') }}@endif" 
                         alt="Bike" 
                         class="w-full h-auto max-h-[400px] lg:max-h-[500px] object-contain transition-all duration-500 ease-in-out" />
                    <!-- Loading overlay -->
                    <div id="image-loading" class="absolute inset-0 bg-gray-100 animate-pulse rounded-lg hidden flex items-center justify-center">
                        <div class="text-gray-500">Loading...</div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Navigation Arrow Only -->
            <div class="col-span-2 flex justify-center items-center">
                <button id="s1-nextBtn" class="w-12 h-12 lg:w-14 lg:h-14 rounded-full border border-gray-300 flex items-center justify-center hover:border-gray-500 hover:bg-gray-50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-300" aria-label="Next model" title="Next Model">
                    <svg class="w-6 h-6 lg:w-7 lg:h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Bottom Section - Logo, Color Selection, Series Link and Variant Tabs -->
        <div class="mt-12 lg:mt-16">
            <!-- Top Row: Left Logo + Right Color Selection -->
            <div class="flex justify-between items-start mb-6">
                <!-- Left Side - Brand Logo and Category -->
                <div class="flex items-center space-x-3">
                    <img id="brand-logo" src="{{ asset('assets/brand-logos/pulsar-logo.png') }}" alt="Brand Logo" class="h-8 w-auto" />
                    <img id="category-icon" src="{{ asset('assets/category-icons/pulsar-category-icon.png') }}" alt="Category Icon" class="h-6 w-auto" />
                    <span id="category-display-text" class="text-sm font-medium text-gray-700">Classic</span>
                </div>

                <!-- Right Side - Color Selection -->
                <div class="flex items-center space-x-4">
                    <!-- Current Color Name -->
                    <h3 id="current-color-name" class="text-sm font-bold text-gray-700 whitespace-nowrap">Charcoal Black</h3>

                    <!-- Color Options -->
                    <div id="color-options" class="flex flex-row items-center space-x-2">
                        @php
                            $defaultColors = [
                                ['id' => 'black', 'name' => 'Charcoal Black', 'class' => 'bg-black'],
                                ['id' => 'yellow', 'name' => 'Racing Yellow', 'class' => 'bg-yellow-400'],
                                ['id' => 'green', 'name' => 'Forest Green', 'class' => 'bg-green-500']
                            ];
                        @endphp
                        @foreach($defaultColors as $index => $color)
                            <button id="s1-color-{{ $color['id'] }}" 
                                    class="w-6 h-6 lg:w-8 lg:h-8 rounded-full border-2 cursor-pointer transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-offset-2 {{ $color['class'] }} {{ $index === 0 ? 'border-gray-800 ring-2 ring-gray-600 scale-110 ring-offset-2' : 'border-gray-300' }}" 
                                    data-color="{{ $index }}" 
                                    data-color-id="{{ $color['id'] }}" 
                                    tabindex="0" 
                                    title="{{ $color['name'] }}">
                            </button>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Series Link and Variant Tabs Container -->
            <div class="flex justify-end">
                <div class="flex flex-col items-start">
                    <!-- View Series Link -->
                    <div class="mb-8">
                        <a href="#" id="s1-seriesLink" class="inline-flex items-center text-black hover:text-gray-700 font-medium transition-colors duration-200 group text-sm lg:text-base">
                            <span class="inline-flex items-center gap-1">
                                View Series page
                                <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M14.3984 6.08398L20.1875 11.7559C20.2812 11.8496 20.3398 11.9629 20.3633 12.0957C20.3867 12.2285 20.3594 12.3574 20.2812 12.4824C20.25 12.5605 20.2109 12.623 20.1641 12.6699L14.4453 18.4824C14.3203 18.5918 14.1758 18.6465 14.0117 18.6465C13.8477 18.6465 13.7109 18.5918 13.6016 18.4824L13.3672 18.248C13.2578 18.123 13.2031 17.9785 13.2031 17.8145C13.2031 17.6504 13.2578 17.5137 13.3672 17.4043L17.6797 13.0215H5.89062C5.73438 13.0215 5.59766 12.9629 5.48047 12.8457C5.36328 12.7285 5.30469 12.584 5.30469 12.4121V12.084C5.30469 11.9277 5.36328 11.791 5.48047 11.6738C5.59766 11.5566 5.73438 11.498 5.89062 11.498H17.75L13.3438 7.18555C13.2344 7.07617 13.1758 6.93555 13.168 6.76367C13.1602 6.5918 13.2188 6.45117 13.3438 6.3418L13.5547 6.10742C13.6797 5.98242 13.8242 5.91992 13.9883 5.91992C14.1523 5.91992 14.2891 5.97461 14.3984 6.08398Z" fill="#326AD2"/>
                                </svg>
                            </span>
                        </a>
                    </div>

                    <!-- Variant Tabs -->
                    <div class="border-t border-gray-300 pt-6">
                        <div id="variant-tabs" class="flex flex-wrap justify-start gap-6 lg:gap-8">
                            @if(isset($featuredBikes) && count($featuredBikes) > 0)
                                @foreach($featuredBikes->where('brand_name', 'PULSAR') as $bike)
                                    <button id="s1-variant-{{ Str::slug($bike->name) }}" 
                                            class="variant-btn px-4 py-2 text-sm font-medium border-b-2 transition-all duration-200 hover:text-black focus:outline-none {{ $loop->first ? 'font-semibold border-black text-black' : 'text-gray-400 border-transparent' }}" 
                                            data-variant="{{ $loop->index }}">
                                        {{ $bike->name }}
                                    </button>
                                @endforeach
                            @else
                                <!-- Default variants -->
                                <button id="s1-variant-pulsar-220f-abs" class="variant-btn px-4 py-2 text-sm font-semibold border-b-2 border-black text-black transition-all duration-200 hover:text-black focus:outline-none" data-variant="0">PULSAR 220F ABS</button>
                                <button id="s1-variant-pulsar-150-td" class="variant-btn px-4 py-2 text-sm font-medium border-b-2 border-transparent text-gray-400 transition-all duration-200 hover:text-black focus:outline-none" data-variant="1">PULSAR 150 TD</button>
                                <button id="s1-variant-pulsar-150" class="variant-btn px-4 py-2 text-sm font-medium border-b-2 border-transparent text-gray-400 transition-all duration-200 hover:text-black focus:outline-none" data-variant="2">PULSAR 150</button>
                                <button id="s1-variant-pulsar-125" class="variant-btn px-4 py-2 text-sm font-medium border-b-2 border-transparent text-gray-400 transition-all duration-200 hover:text-black focus:outline-none" data-variant="3">PULSAR 125</button>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
