"use server";

import { connectToDB } from "@/lib/mongodb";
import Project from "@/models/project";

export const deleteProject = async (projectId) => {
  await connectToDB();

  try {
    await Project.findByIdAndDelete(projectId);
    return;
  } catch (error) {
    console.log("Error in deleteproject action", error);
  }
};

export const getProjectById = async (projectId) => {
  await connectToDB();

  try {
    //finding existing project
    const project = await Project.findById(projectId);
    const data = {
      title: project.title,
      category: project.category,
      desc: project.desc,
      image: project.image,
      githubLink: project.githubLink,
      liveLink: project.liveLink,
    };

    return data;
  } catch (error) {
    console.log("Error in get project by id", error);
  }
};
