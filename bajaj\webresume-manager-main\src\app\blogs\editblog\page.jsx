"use client";

import BlogForm from "@/components/BlogForm";
import { useSession } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect } from "react";

const page = () => {
  const router = useRouter();
  const params = useSearchParams();
  const blogId = params.get("id");

  //redidrect to login if not authenticated
  const { data: session, status } = useSession();

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/");
    }
  }, [status, router]);

  return (
    <div className="px-4 md:px-8 xl:px-16">
      <BlogForm blogId={blogId} />
    </div>
  );
};

export default page;
