import { createContext, useContext, useState, useEffect } from "react";

const TimerContext = createContext();
const ThemeAddTimerContext = createContext();

export function useTimer() {
    return useContext(TimerContext);
}

export function useAddTimer() {
    return useContext(ThemeAddTimerContext);
}

export function GlobalThemeProvider({ children }) {

    const [seconds, setSeconds] = useState(60);

    useEffect(() => {
        if (seconds > 0) {
            const timer = setTimeout(() => setSeconds(seconds - 1), 1000);
            return () => clearTimeout(timer); // Cleanup to avoid memory leaks
        } else {
            setSeconds(60);
        }
    }, [seconds]);
    
    return (
        <TimerContext.Provider value={seconds}>
            <ThemeAddTimerContext.Provider>
                {children}
            </ThemeAddTimerContext.Provider>
        </TimerContext.Provider>
    );
}