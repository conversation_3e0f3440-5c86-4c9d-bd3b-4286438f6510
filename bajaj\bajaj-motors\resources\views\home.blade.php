@extends('layouts.app')

@section('title', 'Bajaj Motors - Premium Motorcycles')
@section('meta_description', 'Discover the latest Bajaj motorcycles with advanced technology, superior performance, and unmatched style.')

@section('content')
    <!-- Hero Section with Carousel -->
    @include('sections.hero-carousel')

    <!-- Bikes Carousel Section -->
    @include('sections.bikes-carousel')

    <!-- Own Your Dream Bajaj Section -->
    @include('sections.dream-bajaj')

    <!-- Experience Section -->
    @include('sections.experiences')

    <!-- Blog Section -->
    @include('sections.blog-section')
@endsection

@push('scripts')
    <!-- Navigation Scripts -->
    <script type="module">
        // Navigation functionality
        window.toggleDropdown = function(dropdownType) {
            const dropdown = document.getElementById(dropdownType + '-dropdown');
            const arrow = document.getElementById(dropdownType + '-arrow');
            
            if (dropdown) {
                dropdown.classList.toggle('hidden');
                if (arrow) {
                    arrow.classList.toggle('rotate-180');
                }
            }
        };

        // Category filtering
        window.filterCategory = function(category) {
            const buttons = document.querySelectorAll('.category-btn');
            buttons.forEach(btn => {
                btn.classList.remove('active', 'bg-blue-600', 'text-white');
                btn.classList.add('bg-gray-100', 'text-gray-700');
            });
            
            event.target.classList.add('active', 'bg-blue-600', 'text-white');
            event.target.classList.remove('bg-gray-100', 'text-gray-700');
            
            // Filter motorcycles by category
            // This would typically make an AJAX call to get filtered data
            console.log('Filtering by category:', category);
        };

        // Mobile menu functionality
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuBtn = document.getElementById('mobile-menu-btn');
            const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');
            const mobileMenuClose = document.getElementById('mobile-menu-close');
            
            if (mobileMenuBtn && mobileMenuOverlay) {
                mobileMenuBtn.addEventListener('click', function() {
                    mobileMenuOverlay.classList.remove('translate-x-full');
                });
            }
            
            if (mobileMenuClose && mobileMenuOverlay) {
                mobileMenuClose.addEventListener('click', function() {
                    mobileMenuOverlay.classList.add('translate-x-full');
                });
            }

            // Mobile bikes menu
            const mobileBikesBtn = document.getElementById('mobile-bikes-btn');
            const mobileBikesDropdown = document.getElementById('mobile-bikes-dropdown');
            const mobileBikesClose = document.getElementById('mobile-bikes-close');
            
            if (mobileBikesBtn && mobileBikesDropdown) {
                mobileBikesBtn.addEventListener('click', function() {
                    mobileBikesDropdown.classList.remove('hidden');
                });
            }
            
            if (mobileBikesClose && mobileBikesDropdown) {
                mobileBikesClose.addEventListener('click', function() {
                    mobileBikesDropdown.classList.add('hidden');
                });
            }

            // Mobile media center submenu
            const mobileMediaBtn = document.getElementById('mobile-media-btn');
            const mobileMediaMenu = document.getElementById('mobile-media-menu');
            const mobileMainMenu = document.getElementById('mobile-main-menu');
            const mobileMediaBack = document.getElementById('mobile-media-back');
            
            if (mobileMediaBtn && mobileMediaMenu && mobileMainMenu) {
                mobileMediaBtn.addEventListener('click', function() {
                    mobileMainMenu.classList.add('hidden');
                    mobileMediaMenu.classList.remove('hidden');
                });
            }
            
            if (mobileMediaBack && mobileMediaMenu && mobileMainMenu) {
                mobileMediaBack.addEventListener('click', function() {
                    mobileMediaMenu.classList.add('hidden');
                    mobileMainMenu.classList.remove('hidden');
                });
            }
        });
    </script>

    <!-- Bike Carousel Scripts -->
    <script type="module">
        // Bike carousel functionality
        class BikeCarousel {
            constructor() {
                this.currentBrand = 'PULSAR';
                this.currentModelIndex = 0;
                this.currentColorId = 'black';
                this.isTransitioning = false;
                
                // Get data from server-side rendered content
                this.bikeData = @json($featuredBikes ?? []);
                
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.updateView();
            }

            setupEventListeners() {
                // Brand tab clicks
                document.addEventListener('click', (e) => {
                    if (e.target.classList.contains('s1-tab')) {
                        e.preventDefault();
                        if (this.isTransitioning) return;
                        const brandName = e.target.getAttribute('data-brand');
                        if (brandName) {
                            this.switchToBrand(brandName);
                        }
                    }
                });

                // Navigation arrows
                const prevBtn = document.getElementById('s1-prevBtn');
                const nextBtn = document.getElementById('s1-nextBtn');
                
                if (prevBtn) {
                    prevBtn.addEventListener('click', () => this.prevSlide());
                }
                
                if (nextBtn) {
                    nextBtn.addEventListener('click', () => this.nextSlide());
                }
            }

            switchToBrand(brandName) {
                this.currentBrand = brandName;
                this.currentModelIndex = 0;
                this.updateView();
                this.updateBrandTabsState(brandName);
            }

            updateView() {
                // Update bike display based on current selection
                const currentBike = this.getCurrentBike();
                if (!currentBike) return;

                const bikeImage = document.getElementById('s1-bikeImage');
                const title = document.getElementById('s1-title');
                const description = document.getElementById('s1-description');

                if (bikeImage) bikeImage.src = currentBike.image || '';
                if (title) title.textContent = currentBike.name || '';
                if (description) description.textContent = currentBike.description || '';
            }

            getCurrentBike() {
                return this.bikeData.find(bike => 
                    bike.brand_name === this.currentBrand
                ) || this.bikeData[0];
            }

            updateBrandTabsState(activeBrand) {
                const brandTabs = document.querySelectorAll('.s1-tab');
                brandTabs.forEach(tab => {
                    const tabBrand = tab.getAttribute('data-brand');
                    if (tabBrand === activeBrand) {
                        tab.classList.remove('text-gray-400', 'border-transparent');
                        tab.classList.add('text-gray-800', 'border-black');
                    } else {
                        tab.classList.remove('text-gray-800', 'border-black');
                        tab.classList.add('text-gray-400', 'border-transparent');
                    }
                });
            }

            nextSlide() {
                if (this.isTransitioning) return;
                const brandBikes = this.bikeData.filter(bike => bike.brand_name === this.currentBrand);
                this.currentModelIndex = (this.currentModelIndex + 1) % brandBikes.length;
                this.updateView();
            }

            prevSlide() {
                if (this.isTransitioning) return;
                const brandBikes = this.bikeData.filter(bike => bike.brand_name === this.currentBrand);
                this.currentModelIndex = this.currentModelIndex === 0 ? brandBikes.length - 1 : this.currentModelIndex - 1;
                this.updateView();
            }
        }

        // Initialize bike carousel when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            new BikeCarousel();
        });
    </script>

    <!-- Hero Carousel Scripts -->
    <script type="module">
        // Hero carousel functionality
        document.addEventListener('DOMContentLoaded', function() {
            const heroSlides = @json($heroSlides ?? []);
            let currentSlide = 0;
            
            const slidesContainer = document.getElementById('carousel-slides');
            const dotsContainer = document.getElementById('carousel-dots');
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');

            if (!slidesContainer || heroSlides.length === 0) return;

            // Create slides
            heroSlides.forEach((slide, index) => {
                const slideElement = document.createElement('div');
                slideElement.className = 'carousel-slide w-full h-full flex-shrink-0 relative';
                slideElement.innerHTML = `
                    <img src="${slide.image}" alt="Hero Slide ${index + 1}" class="w-full h-full object-cover" />
                `;
                slidesContainer.appendChild(slideElement);

                // Create dot
                if (dotsContainer) {
                    const dot = document.createElement('button');
                    dot.className = `w-3 h-3 rounded-full transition-all duration-300 ${index === 0 ? 'bg-white' : 'bg-white/50'}`;
                    dot.addEventListener('click', () => goToSlide(index));
                    dotsContainer.appendChild(dot);
                }
            });

            function goToSlide(index) {
                currentSlide = index;
                slidesContainer.style.transform = `translateX(-${currentSlide * 100}%)`;
                updateDots();
            }

            function updateDots() {
                if (!dotsContainer) return;
                const dots = dotsContainer.querySelectorAll('button');
                dots.forEach((dot, index) => {
                    if (index === currentSlide) {
                        dot.classList.remove('bg-white/50');
                        dot.classList.add('bg-white');
                    } else {
                        dot.classList.remove('bg-white');
                        dot.classList.add('bg-white/50');
                    }
                });
            }

            function nextSlide() {
                currentSlide = (currentSlide + 1) % heroSlides.length;
                goToSlide(currentSlide);
            }

            function prevSlide() {
                currentSlide = currentSlide === 0 ? heroSlides.length - 1 : currentSlide - 1;
                goToSlide(currentSlide);
            }

            if (prevBtn) prevBtn.addEventListener('click', prevSlide);
            if (nextBtn) nextBtn.addEventListener('click', nextSlide);

            // Auto-advance slides
            setInterval(nextSlide, 5000);
        });
    </script>
@endpush
