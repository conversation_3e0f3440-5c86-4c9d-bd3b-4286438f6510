import SignIn from "@/components/Signin";
import { auth } from "@/lib/auth";

export default async function Home() {
  const session = await auth();
  const api_url = process.env.NEXT_PUBLIC_API_URL;
  return (
    <div>
      {!session?.user ? (
        <SignIn />
      ) : (
        <div className="px-4 md:px-8 xl:px-16">
          <h1 className="text-3xl font-semibold">
            Welcome to Portfolio manager
          </h1>
        </div>
      )}

      <div className="px-4 md:px-8 xl:px-16">
        <h2 className="text-2xl font-semibold mt-4">Features</h2>
        <ul className="list-disc ml-6">
          <li>Make your portfolio dynamic with ease by our backend</li>
          <li>
            Add, edit, and remove projects done, experience, blogs and
            testimonials with ease
          </li>
          <li>Access to your details anywhere via api</li>
        </ul>
      </div>

      {session?.user && (
        <div className="px-4 md:px-8 xl:px-16">
          <h2 className="text-2xl font-semibold mt-4">APIs' to access data</h2>
          <ul className="list-disc ml-6">
            <li>
              To get your Blogs - {api_url}/api/blog/{session.user.userId}
            </li>
            <li>
              To get your Projects - {api_url}/api/project/
              {session.user.userId}
            </li>
            <li>
              To get your Experiences - {api_url}/api/experience/
              {session.user.userId}
            </li>
            <li>
              To get your Testimonials - {api_url}/api/testimonial/
              {session.user.userId}
            </li>
          </ul>
        </div>
      )}
    </div>
  );
}
