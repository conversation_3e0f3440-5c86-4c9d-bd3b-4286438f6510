import mongoose from "mongoose";

const TestimonialSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    image: {
      type: String,
    },
    name: {
      type: String,
      required: true,
    },
    designation: {
      type: String,
    },
    company: {
      type: String,
    },
    review: {
      type: String,
      required: true,
    },
    linkedinUrl: {
      type: String,
    },
  },
  { timestamps: true }
);

const Testimonial =
  mongoose.models?.Testimonial ||
  mongoose.model("Testimonial", TestimonialSchema);
export default Testimonial;
