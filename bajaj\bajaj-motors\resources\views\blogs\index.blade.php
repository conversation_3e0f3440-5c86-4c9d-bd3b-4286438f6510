@extends('layouts.app')

@section('title', 'Blogs - Bajaj Motors')
@section('meta_description', 'Read the latest news, stories, and insights from the world of Bajaj motorcycles.')

@section('content')
    <!-- Blogs Hero Section -->
    <section class="relative h-96 overflow-hidden">
        <div class="absolute inset-0">
            <img src="{{ asset('assets/blogsBg.jpg') }}" 
                 alt="Bajaj Blogs" 
                 class="w-full h-full object-cover" />
            <div class="absolute inset-0 bg-black/50"></div>
        </div>
        
        <div class="relative z-10 flex items-center justify-center h-full">
            <div class="text-center text-white max-w-4xl mx-auto px-4">
                <h1 class="text-4xl lg:text-6xl font-bold mb-4">Latest Stories</h1>
                <p class="text-xl lg:text-2xl">
                    Discover the latest news, riding tips, and community stories from the world of Bajaj.
                </p>
            </div>
        </div>
    </section>

    <!-- Featured Blogs Section -->
    @if(isset($featuredBlogs) && count($featuredBlogs) > 0)
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Featured Stories</h2>
                <p class="text-lg text-gray-600">
                    Don't miss these highlighted articles from our editorial team.
                </p>
            </div>

            <div class="grid lg:grid-cols-3 gap-8">
                @foreach($featuredBlogs as $blog)
                    <article class="group cursor-pointer" onclick="window.location.href='{{ route('blogs.show', $blog->slug ?? '#') }}'">
                        <div class="relative h-64 overflow-hidden rounded-lg shadow-lg mb-4">
                            <img src="{{ $blog->image ?? asset('assets/blogs/default-blog.jpg') }}" 
                                 alt="{{ $blog->title }}" 
                                 class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500" />
                            <div class="absolute top-4 left-4">
                                <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-medium">
                                    FEATURED
                                </span>
                            </div>
                        </div>
                        <div class="space-y-2">
                            <div class="text-sm text-gray-500">
                                {{ isset($blog->published_at) ? $blog->published_at->format('M j, Y') : 'Recent' }}
                            </div>
                            <h3 class="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
                                {{ $blog->title }}
                            </h3>
                            <p class="text-gray-600 text-sm">
                                {{ $blog->excerpt ?? Str::limit($blog->content ?? '', 120) }}
                            </p>
                        </div>
                    </article>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- All Blogs Section -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">All Stories</h2>
                <p class="text-lg text-gray-600">
                    Browse through our complete collection of articles and insights.
                </p>
            </div>

            @if(isset($blogs) && count($blogs) > 0)
                <div class="grid lg:grid-cols-3 md:grid-cols-2 gap-8">
                    @foreach($blogs as $blog)
                        <article class="bg-white rounded-lg shadow-lg overflow-hidden group cursor-pointer hover:shadow-xl transition-shadow duration-300" onclick="window.location.href='{{ route('blogs.show', $blog->slug ?? '#') }}'">
                            <div class="relative h-48 overflow-hidden">
                                <img src="{{ $blog->image ?? asset('assets/blogs/default-blog.jpg') }}" 
                                     alt="{{ $blog->title }}" 
                                     class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500" />
                                @if($blog->category ?? false)
                                    <div class="absolute top-4 left-4">
                                        <span class="bg-gray-900 text-white px-3 py-1 rounded-full text-xs font-medium">
                                            {{ strtoupper($blog->category) }}
                                        </span>
                                    </div>
                                @endif
                            </div>
                            <div class="p-6">
                                <div class="text-sm text-gray-500 mb-2">
                                    {{ isset($blog->published_at) ? $blog->published_at->format('M j, Y') : 'Recent' }}
                                </div>
                                <h3 class="text-lg font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                                    {{ $blog->title }}
                                </h3>
                                <p class="text-gray-600 text-sm mb-4">
                                    {{ $blog->excerpt ?? Str::limit($blog->content ?? '', 120) }}
                                </p>
                                <div class="flex items-center text-blue-600 text-sm font-medium">
                                    Read More
                                    <svg class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </div>
                            </div>
                        </article>
                    @endforeach
                </div>

                <!-- Pagination -->
                @if(method_exists($blogs, 'links'))
                    <div class="mt-12 flex justify-center">
                        {{ $blogs->links() }}
                    </div>
                @endif
            @else
                <!-- No blogs state -->
                <div class="text-center py-12">
                    <div class="text-gray-400 mb-4">
                        <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">No Stories Available</h3>
                    <p class="text-gray-600">Check back later for the latest news and stories.</p>
                </div>
            @endif
        </div>
    </section>

    <!-- Newsletter Signup -->
    <section class="py-16 bg-gray-900 text-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl font-bold mb-4">Stay Updated</h2>
            <p class="text-lg text-gray-300 mb-8">
                Subscribe to our newsletter and never miss the latest stories and updates from Bajaj Motors.
            </p>
            
            <form class="max-w-md mx-auto flex gap-4">
                <input type="email" 
                       placeholder="Enter your email" 
                       class="flex-1 px-4 py-3 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500" 
                       required />
                <button type="submit" 
                        class="bg-blue-600 hover:bg-blue-700 px-6 py-3 rounded-lg font-medium transition-colors">
                    Subscribe
                </button>
            </form>
        </div>
    </section>
@endsection
