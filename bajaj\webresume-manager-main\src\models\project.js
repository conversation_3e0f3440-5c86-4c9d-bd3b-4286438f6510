import mongoose from "mongoose";

const projectSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    desc: {
      type: String,
      required: true,
    },
    category: {
      type: String,
      enum: ["frontend", "backend", "mobile"],
    },
    image: {
      type: String,
    },
    githubLink: {
      type: String,
    },
    liveLink: {
      type: String,
    },
    techStackTags: {
      type: [String],
      default: [],
    },
  },
  { timestamps: true }
);

const Project =
  mongoose.models?.Project || mongoose.model("Project", projectSchema);

export default Project;
