"use server";

import { connectToDB } from "@/lib/mongodb";
import Experience from "@/models/experience";

export const deleteExperience = async (experienceId) => {
  await connectToDB();

  try {
    await Experience.findByIdAndDelete(experienceId);
    return;
  } catch (error) {
    console.log("Error in delete experience action", error);
  }
};

export const getExperienceById = async (experienceId) => {
  await connectToDB();

  try {
    //finding existing experience
    const experience = await Experience.findById(experienceId);
    const data = {
      role: experience.role,
      company: experience.company,
      joiningDate: experience.joiningDate,
      resignDate: experience.resignDate,
      desc: experience.desc,
      image: experience.image,
    };

    return data;
  } catch (error) {
    console.log("Error in get blog by id", error);
  }
};
