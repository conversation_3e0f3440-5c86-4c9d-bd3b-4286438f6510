"use client";

import { deleteTestimonial, getTestimonialById } from "@/actions/testimonial";
import { Button } from "@/components/ui/button";
import { useSession } from "next-auth/react";
import { CldUploadWidget } from "next-cloudinary";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";

const TestimonialForm = ({ testimonialId }) => {
  const router = useRouter();
  const [errors, setErrors] = useState({});
  const [success, setSuccess] = useState(false);
  const { data: session, status } = useSession();

  useEffect(() => {
    if (success) {
      testimonialId
        ? toast.success("Testimonial updated successfully!")
        : toast.success("Testimonial added successfully!");
      router.push("/testimonials");
    }
  }, [success, router]);

  //handling form data with image
  const [img, setImg] = useState("");
  const [formData, setFormData] = useState({
    name: "",
    designation: "",
    company: "",
    review: "",
    linkedinUrl: "",
  });

  //get details of existing testimonial in case of edit
  useEffect(() => {
    const getTestimonial = async () => {
      if (testimonialId) {
        const testimonial = await getTestimonialById(testimonialId);
        setFormData({
          name: testimonial.name,
          designation: testimonial.designation,
          company: testimonial.company,
          review: testimonial.review,
          linkedinUrl: testimonial.linkedinUrl,
        });
        setImg(testimonial.image);
      }
    };

    getTestimonial();
  }, [testimonialId]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    const userId = session.user.userId;
    //console.log(session);
    const res = await fetch(`/api/testimonial/${userId}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        name: formData.name,
        designation: formData.designation,
        company: formData.company,
        review: formData.review,
        linkedinUrl: formData.linkedinUrl,
        image: img || "",
      }),
    });

    const data = await res.json();
    if (res.ok) {
      //console.log("Post created:", data);
      setFormData({
        name: "",
        designation: "",
        company: "",
        review: "",
        linkedinUrl: "",
      });
      setImg("");
      setSuccess(true);
      setErrors({});

      //delete old testimonial after updating
      if (testimonialId) {
        await deleteTestimonial(testimonialId);
      }
    } else {
      //console.log("errror", data);
      setErrors(data.error || { general: [data.error] });
      setSuccess(false);
    }
  };

  if (status === "loading") return <p>Loading...</p>;

  return (
    <div className="px-4 md:px-8 xl:px-16">
      <div className="flex items-center gap-16 mb-12">
        <h1 className="text-3xl font-semibold">
          {testimonialId ? "Update Testimonial" : "Add New Testimonial"}
        </h1>
      </div>

      <form onSubmit={handleSubmit} className="flex flex-col gap-6">
        <div className="flex flex-col">
          <label htmlFor="name">Name</label>
          <input
            type="text"
            name="name"
            placeholder="Enter name"
            value={formData.name}
            onChange={(e) =>
              setFormData({
                ...formData,
                name: e.target.value,
              })
            }
            className="border border-gray-400 px-2 py-1 outline-none"
            required
          />
          {errors.name && <p className="text-red-500">{errors.name[0]}</p>}
        </div>

        <CldUploadWidget
          uploadPreset="portfolio-backend"
          onSuccess={(result) => setImg(result?.info.secure_url)}
        >
          {({ open }) => {
            return (
              <div className="flex flex-col">
                <label htmlFor="image">Image</label>
                <Image
                  src={img || "/no-image.png"}
                  width={140}
                  height={100}
                  alt="image"
                  className="w-20 h-auto rounded-lg"
                />
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    open();
                  }}
                  name="imageUrl"
                  className="border border-gray-400 px-2 py-1 outline-none cursor-pointer"
                >
                  Choose Image
                </button>
              </div>
            );
          }}
        </CldUploadWidget>

        <div className="flex flex-col">
          <label htmlFor="designation">Designation</label>
          <input
            type="text"
            name="designation"
            placeholder="Enter designation"
            value={formData.designation}
            onChange={(e) =>
              setFormData({
                ...formData,
                designation: e.target.value,
              })
            }
            className="border border-gray-400 px-2 py-1 outline-none"
            required
          />
          {errors.designation && (
            <p className="text-red-500">{errors.designation[0]}</p>
          )}
        </div>

        <div className="flex flex-col">
          <label htmlFor="company">Company</label>
          <input
            type="text"
            name="company"
            placeholder="Enter company"
            value={formData.company}
            onChange={(e) =>
              setFormData({
                ...formData,
                company: e.target.value,
              })
            }
            className="border border-gray-400 px-2 py-1 outline-none"
            required
          />
          {errors.company && (
            <p className="text-red-500">{errors.company[0]}</p>
          )}
        </div>

        <div className="flex flex-col">
          <label htmlFor="linkedinUrl">Linkedin URL</label>
          <input
            type="text"
            name="linkedinUrl"
            placeholder="Enter Linkedin URL"
            value={formData.linkedinUrl}
            onChange={(e) =>
              setFormData({
                ...formData,
                linkedinUrl: e.target.value,
              })
            }
            className="border border-gray-400 px-2 py-1 outline-none"
            required
          />
          {errors.linkedinUrl && (
            <p className="text-red-500">{errors.linkedinUrl[0]}</p>
          )}
        </div>

        <div className="flex flex-col">
          <label htmlFor="desc">Testimonial</label>
          <textarea
            name="review"
            cols="40"
            rows="8"
            value={formData.review}
            onChange={(e) =>
              setFormData({
                ...formData,
                review: e.target.value,
              })
            }
            placeholder="Write your testimonial here"
            className="border border-gray-400 px-2 py-1 outline-none"
            required
          ></textarea>
          {errors.desc && <p className="text-red-500">{errors.desc[0]}</p>}
        </div>

        <Button
          type="submit"
          className="bg-gray-800 text-white cursor-pointer hover:text-stone-200 mb-10"
        >
          {testimonialId ? "Update Testimonial" : "Add Testimonial"}
        </Button>
      </form>
    </div>
  );
};

export default TestimonialForm;
