"use client";

import ProjectForm from "@/components/ProjectForm";
import { useSession } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect } from "react";

const page = () => {
  const router = useRouter();
    const params = useSearchParams();
  

  //redidrect to login if not authenticated
  const { data: session, status } = useSession();
  const projectId = params.get("id");

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/");
    }
  }, [status, router]);

  return (
    <div className="px-4 md:px-8 xl:px-16">
      <ProjectForm projectId={projectId} />
    </div>
  );
};

export default page;
