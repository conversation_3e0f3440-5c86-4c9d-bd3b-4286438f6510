import React, { useEffect, useState, useRef } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Check,
  Clear,
  Close,
  Download,
  FilterList,
} from "@mui/icons-material";
import TableComponent from "../TableComponent";
import httpclient from "../../../Utils";
import {
  Box,
  Button,
  Card,
  Collapse,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  styled,
  TextField,
  Snackbar,
  Autocomplete,
} from "@mui/material";
// import ViewOrderDialog from "../ViewOrderDialog";
import MuiAlert from "@mui/material/Alert";
// import StatusDialog from "../StatusDialog";
// import BackdropLoader from "../../../../Components/BackdropLoader";
import { useLocation, useNavigate } from "react-router";
import ViewCustomerDetail from "./ViewCustomerDetail";
import useTokenRefresh from "../../../Hooks/useTokenRefresh";
import Footer from "../../../Components/Footer";


//import { useLocation } from "react-router-dom";

const Alert = React.forwardRef(function Alert(props, ref) {
  return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});



const FilteredBox = styled(Box)(({ theme }) => ({
  background: "#f9f9f9",
  padding: "5px 10px",
  borderRadius: "5px",
  "& p": {
    margin: "3px 0",
    marginRight: "10px",
    display: "inline-block",
    background: "#dedede",
    borderRadius: "10px",
    padding: "2px 5px",
  },
  "& svg": {
    fontSize: "15px",
    cursor: "pointer",
    position: "relative",
    top: "3px",
    background: theme.palette.primary.dark,
    color: "#fff",
    borderRadius: "50%",
    padding: "2px",
    marginLeft: "2px",
  },
}));

const Header = styled("div")(({ theme }) => ({
  "& h1": {
    color: theme.palette.primary.dark,
    margin: "0",
  },
}));

const configRowPerPage = JSON.parse(localStorage.getItem("configRowPerPage"));

const NetSuiteCustomers = (props) => {

  const columns = [
    { id: "internalID", name: "Internal ID" },
    { id: "fullname", name: "Full Name" },
    { id: "email", name: "Email" },
    { id: "phone", name: "Phone" },
    { id: "isperson", name: "Person/Company" },
    { id: "lastmodifieddate", name: "Last Modified Date" },
  ];

  const { getTokenRefreshed: refresh, open: tokenOpen, overlay: overlay, setOpen: setTokenOpen, message: tokenMessage, messageState: tokenMessageState } = useTokenRefresh();

  const location = useLocation();
  const navigate = useNavigate();
  const buttonRef = useRef(null);

  const [openViewDialog, setOpenViewDialog] = useState(false);
  const [viewDetails, setViewDetails] = useState({});
  const [openStatusDialog, setOpenStatusDialog] = useState(false);
  const [customStatus, setCustomStatus] = useState([]);
  const [exceptionStatus, setExceptionStatus] = useState([]);
  const [rows, setRows] = useState([]);
  const [exportRows, setExportRows] = useState("");
  const [rowChecked, setRowChecked] = useState([]);

  const [buttonLoader, setButtonLoader] = useState(false);
  const [backdropLoader, setBackdropLoader] = useState(false);
  const [loading, setLoading] = useState(false);
  const [singleLoading, setSingleLoading] = useState(false);
  const [direction, setDirection] = useState(false);
  const [currentColumn, setCurrentColumn] = useState("");
  const [page, setPage] = useState(1);
  const [from, setFrom] = useState(1);
  const [to, setTo] = useState(
    configRowPerPage && configRowPerPage
      ? configRowPerPage && configRowPerPage
      : 20
  );

  const [rowsPerPage, setRowsPerPage] = useState(
    configRowPerPage && configRowPerPage
      ? configRowPerPage && configRowPerPage
      : 20
  );
  const [total, setTotal] = useState("");
  const [filterOpen, setFilterOpen] = useState(false);

  const [open, setOpen] = useState(false);
  const [message, setMessage] = useState("");
  const [messageState, setMessageState] = useState("");
  const [companyList, setCompanyList] = useState([]);
  const [filterData, setFilterData] = useState({

    email: "",
    phone: "",
    internalID: "",
    startDate: "",
    endDate: "",
    remove: false,
  });

  const [submittedData, setSubmittedData] = useState({
    email: "",
    phone: "",
    internalID: "",
    startDate: "",
    endDate: "",

    submit: false,
  });

  useEffect(() => {
    if (
      filterData.email === "" ||
      filterData.phone === "" ||
      filterData.internalID === "" ||
      filterData.startDate === "" ||
      filterData.endDate === ""

    ) {
      setSubmittedData({
        ...submittedData,
        submit: false,
      });
    }

    if (filterData.email === " ") filterData.email = "";
    if (filterData.phone === " ") filterData.phone = "";
    if (filterData.internalID === " ") filterData.internalID = "";
    if (filterData.startDate === " ") filterData.startDate = "";
    if (filterData.endDate === " ") filterData.endDate = "";

    filterData.remove === true && handleFilter();
  }, [filterData]);

  useEffect(() => {
    let currentpolicy = JSON.parse(localStorage.getItem("netsuite_customer_filter"));
    currentpolicy !== null && setFilterData(currentpolicy);

    currentpolicy == null
      ? getNetSuiteCustomers()
      :
        currentpolicy.email == "" &&
        currentpolicy.phone == "" &&
        currentpolicy.internalID == "" &&
        currentpolicy.startDate == "" &&
        currentpolicy.endDate == "" &&

        currentpolicy.removed == false
        ? getNetSuiteCustomers()
        : console.log("customers");
  }, []);

    useEffect(() => {
      if (location.state !== null) {
        if (location.state?.id) {
          filterData.internalID = location.state.value
          setTimeout(() => {
            handleFilter();
            navigate("#", { replace: true });
            handleView(location.state.id);
          }, 1500);
        }
        if (location.state?.startDate) {
          filterData.startDate = location.state.startDate
          filterData.endDate = location.state.endDate
          setTimeout(() => {
            handleFilter();
            navigate("#", { replace: true });
          }, 1500);
        }
        setTimeout(() => {
          navigate("#", { replace: true });
        }, 1500);
      }
    }, [location.state]);


  const getNetSuiteCustomers = () => {
    setLoading(true);
    httpclient
      .get(`request-response?requestName=netsuite/customers&pagination=${rowsPerPage}&page=${page}`)
      .then(({ data }) => {
        if (data.status === 200) {
          setRows(data.data);
          setTotal(data.meta.total);
          setRowsPerPage(parseInt(data.meta.per_page));
          setPage(data.meta.current_page);
          setFrom(data.meta.from);
          setTo(data.meta.to);
          setLoading(false);
        } else {
          setOpen(true);
          setMessage(data.message);
          setMessageState("error");
          setLoading(false);
        }

      }).catch((err) => {
        if (err.response.status === 401) {
          refresh();
          setOpen(tokenOpen);
          setMessage(tokenMessage);
          setMessageState("error");
        } else if (err.response.status === 422) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setLoading(false);
        } else if (err.response.status === 400) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setLoading(false);

        } else {
          setOpen(true);
          setMessage(err.response.data.message);
          setMessageState("error");
          setLoading(false);
        }
      })
  };


  const handleView = (row) => {
    setSingleLoading(true);
    setOpenViewDialog(true);
    httpclient
      .get(`request-response?requestName=netsuite/customers/${row.internalID || row}`)
      .then(({ data }) => {
        if (data) {
          setViewDetails(data.data);
          setSingleLoading(false);
        }
        else {
          setOpen(true);
          setMessage(data.message);
          setMessageState("error");
          setLoading(false);
        }

      }).catch((err) => {
        if (err.response.status === 401) {
          refresh();
          setOpen(tokenOpen);
          setMessage(tokenMessage);
          setMessageState("error");
        } else if (err.response.status === 422) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setLoading(false);
        } else if (err.response.status === 400) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setLoading(false);

        } else {
          setOpen(true);
          setMessage(err.response.data.message);
          setMessageState("error");
          setLoading(false);
        }
      })
  };

  const sendDetails = (callback) => {
    if (callback.open === false) {
      setOpenViewDialog(false);
      setViewDetails({});
    }
    if (callback.refetch === true) {
      handleView(callback.internalID);
      setTimeout(() => {
        handleFilter();
      }, 1000);
    }
  };



  const handleFilter = () => {
    setSubmittedData({
      ...submittedData,
      internalID: filterData.internalID,
      email: filterData.email,
      phone: filterData.phone,
      startDate: filterData.startDate,
      endDate: filterData.endDate,
      submit: true,
    });

    filterData.remove = true;

    localStorage.setItem("netsuite_customer_filter", JSON.stringify(filterData));


    setLoading(true);
    if (
      filterData.internalID ||
      filterData.email ||
      filterData.phone ||
      filterData.startDate ||
      filterData.endDate


    ) {

      httpclient
        .get(
          `request-response?requestName=netsuite/customers&filters[email][$eq]=${filterData.email
          }&filters[phone][$eq]=${filterData.phone
          }&filters[internalID][$eq]=${filterData.internalID
          }&filters[lastmodifieddate][$between][0]=${filterData.startDate
          }&filters[lastmodifieddate][$between][1]=${filterData.endDate
          }&pagination=${rowsPerPage}&page=${1}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setTotal(data.meta.total);
            setRowsPerPage(data.meta.per_page);
            setPage(data.meta.current_page);
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }
        });
    } else {
      getNetSuiteCustomers();
    }
  };



  const hadleFilterOpen = () => {
    setFilterOpen((prev) => !prev);
  };

  const handleChangeFilter = (e) => {
    const { name, value } = e.target;
    setFilterData({
      ...filterData,
      [name]: value,
      remove: false,
    });
  };
  // console.log('filter data', filterData);

  const handleRemove = (data) => {
    setExportRows("");
    if (data === "soldTo") {
      filterData.soldToName = "";
      submittedData.soldToName = "";
    }
    if (data === "customStatus") {
      filterData.customStatusName = "";
      submittedData.customStatusName = "";
    }
    if (data === "exceptionStatus") {
      filterData.exceptionStatusName = "";
      submittedData.exceptionStatusName = "";
    }
    if (data === "startDate") {
      setFilterData({
        ...filterData,
        startDate: "",
        endDate: "",
        remove: true,
      });
      setSubmittedData({
        ...submittedData,
        startDate: "",
        endDate: "",
      });
    } else if (data === "despatchByStartDate") {
      setFilterData({
        ...filterData,
        despatchByStartDate: "",
        despatchByEndDate: "",
        remove: true,
      });
      setSubmittedData({
        ...submittedData,
        despatchByStartDate: "",
        despatchByEndDate: "",
      });
    } else {
      setFilterData({
        ...filterData,
        [data]: "",
        remove: true,
      });

      setSubmittedData({
        ...submittedData,
        [data]: "",
      });
    }
  };


  const handleSort = (column) => {
    setDirection((prevDirection) => !prevDirection);
    setCurrentColumn(column);
    setLoading(true);
    submittedData.submit
      ? httpclient
        .get(
          `request-response?requestName=netsuite/customers&filters[email][$eq]=${filterData.email
          }&filters[phone][$eq]=${filterData.phone
          }&filters[internalID][$eq]=${filterData.internalID
          }&filters[lastmodifieddate][$between][0]=${filterData.startDate
          }&filters[lastmodifieddate][$between][1]=${filterData.endDate
          }&sort[0]=${column}:${!direction ? "asc" : "desc"
          }&pagination=${rowsPerPage}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setTotal(data.meta.total);
            setRowsPerPage(parseInt(data.meta.per_page));
            setPage(data.meta.current_page);
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }
        })

      : httpclient
        .get(
          `request-response?requestName=netsuite/customers&sort[0]=${column}:${!direction ? "asc" : "desc"
          }&pagination=${rowsPerPage}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }

        });
  };

  const handleChangePage = (e, page) => {
    setLoading(true);
    submittedData.submit
      ? httpclient
        .get(
          `request-response?requestName=netsuite/customers&filters[email][$eq]=${filterData.email
          }&filters[phone][$eq]=${filterData.phone
          }&filters[internalID][$eq]=${filterData.internalID
          }&filters[lastmodifieddate][$between][0]=${filterData.startDate
          }&filters[lastmodifieddate][$between][1]=${filterData.endDate
          }${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''
          }&pagination=${rowsPerPage}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setTotal(data.meta.total);
            setRowsPerPage(parseInt(data.meta.per_page));
            setPage(data.meta.current_page);
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }

        })
      : httpclient
        .get(
          `request-response?requestName=netsuite/customers${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''}&pagination=${rowsPerPage}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setTotal(data.meta.total);
            setRowsPerPage(parseInt(data.meta.per_page));
            setPage(data.meta.current_page);
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }

        });
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(+event.target.value);
    setLoading(true);

    localStorage.setItem("configRowPerPage", event.target.value);

    submittedData.submit
      ? httpclient
        .get(
          `request-response?requestName=netsuite/customers&filters[email][$eq]=${filterData.email
          }&filters[phone][$eq]=${filterData.phone
          }&filters[internalID][$eq]=${filterData.internalID
          }&filters[lastmodifieddate][$between][0]=${filterData.startDate
          }&filters[lastmodifieddate][$between][1]=${filterData.endDate
          }${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''
          }&pagination=${+event.target.value}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setTotal(data.meta.total);
            setRowsPerPage(parseInt(data.meta.per_page));
            setPage(data.meta.current_page);
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }
        })
      : httpclient
        .get(
          `request-response?requestName=netsuite/customers${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''}&pagination=${+event.target.value}&page=${1}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setTotal(data.meta.total);
            setRowsPerPage(parseInt(data.meta.per_page));
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setPage(data.meta.current_page);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }
        })
  };

  const handleClose = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }
    setOpen(false);
    setTokenOpen(false);
  };


  return (
    <div>
      <Grid container spacing={2}>
        <Grid item md={8} xs={12}>
          <Header>
            <h1>List Customers (NetSuite)</h1>
          </Header>
        </Grid>
        <Grid
          item
          md={4}
          xs={12}
          display="flex"
          alignItems="center"
          justifyContent="flex-end"
        >
          <Button color="primary" variant="contained" onClick={hadleFilterOpen}>
            Filter <FilterList style={{ marginLeft: "5px" }} fontSize="small" />
          </Button>
        </Grid>

        {/* Filter */}
        <Grid item xs={12}>
          <Collapse in={filterOpen}>
            <Card>
              <Box p={4}>
                <Grid container spacing={2}>

                  <Grid item xs={12} md={4}>
                    <InputLabel>Internal ID</InputLabel>
                    <TextField
                      name="internalID"
                      value={filterData.internalID}
                      onChange={handleChangeFilter}
                      onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                      variant="outlined"
                      fullWidth
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <InputLabel>Email</InputLabel>
                    <TextField
                      name="email"
                      value={filterData.email}
                      onChange={handleChangeFilter}
                      onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                      variant="outlined"
                      fullWidth
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <InputLabel>Phone</InputLabel>
                    <TextField
                      name="phone"
                      value={filterData.phone}
                      onChange={handleChangeFilter}
                      onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                      variant="outlined"
                      fullWidth
                    />

                  </Grid>
                  <Grid item xs={12} md={4}>
                    <InputLabel>Start Date</InputLabel>
                    <TextField
                      variant="outlined"
                      name="startDate"
                      type="date"
                      value={filterData.startDate}
                      onChange={(e) => handleChangeFilter(e)}
                      fullWidth
                      InputLabelProps={{
                        shrink: true,
                      }}
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <InputLabel>End Date</InputLabel>
                    <TextField
                      variant="outlined"
                      name="endDate"
                      type="date"
                      value={filterData.endDate}
                      onChange={(e) => handleChangeFilter(e)}
                      fullWidth
                      InputLabelProps={{
                        shrink: true,
                      }}
                    />
                  </Grid>


                  <Grid item xs={12}>
                    <Box textAlign={"right"}>
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={handleFilter}
                      >
                        Filter{" "}
                        <ArrowForward
                          fontSize="small"
                          style={{ marginLeft: "5px" }}
                        />
                      </Button>
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            </Card>
          </Collapse>
        </Grid>

        {
            submittedData.email ||
            submittedData.phone ||
            submittedData.internalID ||
            submittedData.startDate ||
            submittedData.endDate
            ? (
              <Grid item xs={12}>
                <FilteredBox>
                  <span>Filtered: </span>

                  {submittedData.internalID && (
                    <p>
                      <span>Internal ID: {submittedData.internalID}</span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("internalID")}
                      />
                    </p>
                  )}
                  {submittedData.email && (
                    <p>
                      <span>Email: {submittedData.email}</span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("email")}
                      />
                    </p>
                  )}
                  {submittedData.phone && (
                    <p>
                      <span>Phone: {submittedData.phone}</span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("phone")}
                      />
                    </p>
                  )}
                  {(submittedData.startDate || submittedData.endDate) && (
                    <p>
                      <span>
                        Last Modified Date Range: {submittedData.startDate} -{" "}
                        {submittedData.endDate}
                      </span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("startDate")}
                      />
                    </p>
                  )}



                </FilteredBox>
              </Grid>
            ) : (
              <Box></Box>
            )}
        {/* Filter */}



        <Grid item xs={12}>
          <TableComponent
            columns={columns}
            rows={rows}
            sort={true}
            handleView={handleView}
            handleSort={handleSort}
            loading={loading}
            handleChangeRowsPerPage={handleChangeRowsPerPage}
            handleChangePage={handleChangePage}
            rowChecked={rowChecked}
            buttonLoader={buttonLoader}
            direction={direction}
            currentColumn={currentColumn}
            page={page}
            total={total && total}
            fromTable={from}
            toTable={to}
            rowsPerPage={rowsPerPage}
            filterData={filterData}
          />
        </Grid>
        <Footer overlay={overlay || props.overlayNew} />
      </Grid>

      {openViewDialog && (
        <ViewCustomerDetail
          singleLoading={singleLoading}
          viewDetails={viewDetails}
          sendDetails={sendDetails}
          loginValue={props.loginValue}
        />
      )}


      {/* {openStatusDialog && (
        <StatusDialog
          sendChangeOrder={sendChangeOrder}
          status={status}
          selectedChecked={selectedChecked}
          viewDetails={viewDetails}
        />
      )} */}

      {/* {backdropLoader && <BackdropLoader />} */}

      <Snackbar
        autoHideDuration={3000}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
        open={open || tokenOpen}
        onClose={handleClose}
      >
        <Alert
          onClose={handleClose}
          severity={messageState || tokenMessageState}
          sx={{ width: "100%" }}
        >
          {message || tokenMessage}
        </Alert>
      </Snackbar>
    </div>
  );
};

export default NetSuiteCustomers;
