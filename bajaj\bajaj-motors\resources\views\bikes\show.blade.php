@extends('layouts.app')

@section('title', ($bike->name ?? 'Bike Details') . ' - Bajaj Motors')
@section('meta_description', $bike->description ?? 'Discover the features and specifications of this amazing Bajaj motorcycle.')

@section('content')
    <!-- Bike Hero Section -->
    <section class="relative h-96 overflow-hidden">
        <div class="absolute inset-0">
            <img src="{{ $bike->image ?? asset('assets/bikes/default-bike.jpg') }}" 
                 alt="{{ $bike->name ?? 'Bike' }}" 
                 class="w-full h-full object-cover" />
            <div class="absolute inset-0 bg-black/50"></div>
        </div>
        
        <div class="relative z-10 flex items-center justify-center h-full">
            <div class="text-center text-white max-w-4xl mx-auto px-4">
                <h1 class="text-4xl lg:text-6xl font-bold mb-4">{{ $bike->name ?? 'Bike Name' }}</h1>
                <p class="text-xl lg:text-2xl mb-6">
                    {{ $bike->description ?? 'Experience the power and performance of this amazing motorcycle.' }}
                </p>
                @if($bike->price ?? false)
                    <div class="text-3xl font-bold text-yellow-400">
                        Starting at {{ $bike->price }}
                    </div>
                @endif
            </div>
        </div>
    </section>

    <!-- Bike Gallery -->
    @if(isset($bike->gallery) && is_array($bike->gallery) && count($bike->gallery) > 0)
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Gallery</h2>
                <p class="text-lg text-gray-600">Take a closer look at every detail</p>
            </div>
            
            <div class="grid lg:grid-cols-3 md:grid-cols-2 gap-6">
                @foreach($bike->gallery as $image)
                    <div class="relative h-64 overflow-hidden rounded-lg shadow-lg group cursor-pointer">
                        <img src="{{ $image }}" 
                             alt="{{ $bike->name }}" 
                             class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500" />
                    </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- Specifications -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Specifications</h2>
                <p class="text-lg text-gray-600">Technical details and performance metrics</p>
            </div>

            <div class="grid lg:grid-cols-2 gap-8">
                <!-- Engine Specs -->
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <h3 class="text-2xl font-bold text-gray-900 mb-6">Engine & Performance</h3>
                    <div class="space-y-4">
                        @if($bike->engine ?? false)
                            <div class="flex justify-between border-b border-gray-200 pb-2">
                                <span class="font-medium text-gray-700">Engine</span>
                                <span class="text-gray-900">{{ $bike->engine }}</span>
                            </div>
                        @endif
                        @if($bike->power ?? false)
                            <div class="flex justify-between border-b border-gray-200 pb-2">
                                <span class="font-medium text-gray-700">Max Power</span>
                                <span class="text-gray-900">{{ $bike->power }}</span>
                            </div>
                        @endif
                        @if($bike->torque ?? false)
                            <div class="flex justify-between border-b border-gray-200 pb-2">
                                <span class="font-medium text-gray-700">Max Torque</span>
                                <span class="text-gray-900">{{ $bike->torque }}</span>
                            </div>
                        @endif
                        @if($bike->top_speed ?? false)
                            <div class="flex justify-between border-b border-gray-200 pb-2">
                                <span class="font-medium text-gray-700">Top Speed</span>
                                <span class="text-gray-900">{{ $bike->top_speed }}</span>
                            </div>
                        @endif
                        @if($bike->mileage ?? false)
                            <div class="flex justify-between">
                                <span class="font-medium text-gray-700">Mileage</span>
                                <span class="text-gray-900">{{ $bike->mileage }}</span>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- General Specs -->
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <h3 class="text-2xl font-bold text-gray-900 mb-6">General</h3>
                    <div class="space-y-4">
                        @if($bike->weight ?? false)
                            <div class="flex justify-between border-b border-gray-200 pb-2">
                                <span class="font-medium text-gray-700">Weight</span>
                                <span class="text-gray-900">{{ $bike->weight }}</span>
                            </div>
                        @endif
                        @if($bike->fuel_capacity ?? false)
                            <div class="flex justify-between border-b border-gray-200 pb-2">
                                <span class="font-medium text-gray-700">Fuel Capacity</span>
                                <span class="text-gray-900">{{ $bike->fuel_capacity }}</span>
                            </div>
                        @endif
                        @if($bike->price ?? false)
                            <div class="flex justify-between">
                                <span class="font-medium text-gray-700">Price</span>
                                <span class="text-gray-900 font-bold text-lg">{{ $bike->price }}</span>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features -->
    @if(isset($bike->features) && is_array($bike->features) && count($bike->features) > 0)
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Key Features</h2>
                <p class="text-lg text-gray-600">What makes this bike special</p>
            </div>

            <div class="grid lg:grid-cols-3 md:grid-cols-2 gap-6">
                @foreach($bike->features as $feature)
                    <div class="bg-gray-50 rounded-lg p-6 text-center">
                        <div class="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">{{ $feature }}</h3>
                    </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- CTA Section -->
    <section class="py-16 bg-gray-900 text-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl font-bold mb-4">Ready to Experience {{ $bike->name ?? 'This Bike' }}?</h2>
            <p class="text-lg text-gray-300 mb-8">
                Book a test ride today and feel the power and performance for yourself.
            </p>
            
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('book-test-ride') }}" 
                   class="bg-blue-600 hover:bg-blue-700 px-8 py-3 rounded-lg font-medium transition-colors">
                    Book Test Ride
                </a>
                <a href="{{ route('showrooms') }}" 
                   class="border border-white hover:bg-white hover:text-gray-900 px-8 py-3 rounded-lg font-medium transition-colors">
                    Find Showroom
                </a>
            </div>
        </div>
    </section>

    <!-- Back to Bikes -->
    <section class="py-8 bg-white border-t border-gray-200">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <a href="{{ route('home') }}#bikes" 
                   class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                    Back to All Bikes
                </a>
            </div>
        </div>
    </section>
@endsection
