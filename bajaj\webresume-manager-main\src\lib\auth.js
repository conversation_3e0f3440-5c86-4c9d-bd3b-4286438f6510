import User from "@/models/user";
import NextAuth from "next-auth";
import Google from "next-auth/providers/google";
import { connectToDB } from "./mongodb";

export const { handlers, auth, signIn, signOut } = NextAuth({
  providers: [Google],

  callbacks: {
    //1. handle sign in
    async signIn({ user }) {
      await connectToDB();

      const existingUser = await User.findOne({
        email: user.email,
      });

      if (!existingUser) {
        const newUser = new User({
          name: user.name,
          email: user.email,
        });

        existingUser = await newUser.save();
      }

      user._id = existingUser._id.toString(); // ✅ attach MongoDB _id
      return true;
    },

    // 2. Store the user ID in the JWT token
    async jwt({ token, user }) {
      if (user?._id) {
        token.userId = user._id;
      }
      return token;
    },

    // 3. Make the userId available in session
    async session({ session, token }) {
      if (token?.userId) {
        session.user.userId = token.userId;
      }
      return session;
    },
  },
});
