import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Clear, Close, Done, Download, Edit, KeyboardArrowLeft, LockOpen, MultipleStop, Sync, ViewStream, Visibility } from "@mui/icons-material";
import {
    AppBar,
    Box,
    Button,
    CircularProgress,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Grid,
    IconButton,
    Skeleton,
    styled,
    useTheme,
    Snackbar,
    FormControl,
    Tooltip,
    Select,
    MenuItem
} from "@mui/material";
import moment from "moment";
import React, { useEffect, useState } from "react";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import Typography from "@mui/material/Typography";
import PropTypes from "prop-types";
//import BasicTable from "../../../../Components/BasicTable";
import FsLightbox from "fslightbox-react";

import MuiAlert from "@mui/material/Alert";
//import ViewPolicyDialog from "../../price_policy/ViewPolicyDialog";
import parse from "html-react-parser";
import BasicTable from "../../../../Components/BasicTable";
import httpclient from "../../../../Utils";
import useTokenRefresh from "../../../../Hooks/useTokenRefresh";
import BasicTableShopify from "../../../../Components/BasicTableShopify";


const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

const StyledHeaderTitle = styled(DialogTitle)(({ theme }) => ({
    background: theme.palette.primary.main,
    color: "#fff",
    position: "relative",
    "& button": {
        position: "absolute",
        right: "15px",
        top: "15px",
        color: "#fff",
    },
}));

const FlexContent = styled("div")(({ theme }) => ({
    display: "flex",
    flexDirection: "row",
    fontSize: "17px",
    marginBottom: "10px",
    alignItems: "flex-start",
}));

const FlexInnerTitle = styled("div")(({ theme }) => ({
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    minWidth: "249px",
    maxWidth: "250px",
    fontWeight: "600",
}));

const FlexContent2 = styled("div")(({ theme }) => ({
    display: "flex",
    flexDirection: "row",
    fontSize: "17px",
}));

const FlexInnerTitle2 = styled("div")(({ theme }) => ({
    display: "flex",
    fontWeight: "600",
    gap: "5px",
    marginRight: "5px",
}));

const BoxDiv = styled("div")(({ theme }) => ({
    textAlign: "center",
}));

const Values = styled("div")(({ theme }) => ({
    marginLeft: "10px",
    fontWeight: "500",
    color: theme.palette.primary.dark,
}));

const ImageDiv = styled("div")(({ theme }) => ({
    display: "flex",
    alignItems: "center",
    width: "100%",
    flexWrap: "wrap",
    marginBottom: "10px",
}));

const ImageCell = styled("div")(({ theme }) => ({
    margin: "10px",
    width: "280px",
    borderRadius: "5px",
    overflow: "hidden",
    "& img": {
        width: "250px",
        height: "250px",
        objectFit: "cover",
        transition: "0.5s",
        boxShadow: theme.palette.primary.shadow,
        marginBottom: "10px",
        overflow: "hidden",
    },
    "& img:hover": {
        transform: "scale(1.1)",
    },
}));

const price_policyBox = styled(Box)(({ theme }) => ({
    display: "flex",
    marginBottom: "15px",
    "& h5": {
        margin: "5px",
    },
}));

const AppBarTabs = styled(AppBar)(({ theme }) => ({
    background: "#fff",
    color: theme.palette.primary.dark,
}));

function TabPanel(props) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`full-width-tabpanel-${index}`}
            aria-labelledby={`full-width-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3 }}>
                    <Typography>{children}</Typography>
                </Box>
            )}
        </div>
    );
}

TabPanel.propTypes = {
    children: PropTypes.node,
    index: PropTypes.number.isRequired,
    value: PropTypes.number.isRequired,
};

function a11yProps(index) {
    return {
        id: `full-width-tab-${index}`,
        "aria-controls": `full-width-tabpanel-${index}`,
    };
}

const orderColumns = [
    { id: "stockCode", name: "Stock Code" },
    { id: "sku", name: "SKU" },
    { id: "size", name: "Size" },
    { id: "color", name: "Color" },
    { id: "quantity", name: "Quantity" },
    { id: "unit_price", name: "Unit Price" },
    { id: "total_price", name: "Total Price" },
    { id: "assign_to_branch", name: "Location" },
    { id: "updated_at", name: "Updated Date" },

];

const sohColumns = [
    //{ id: "checkColumn", name: " " },
    { id: "erplyWarehouseID", name: "Warehouse ID" },
    { id: "warehouse_details", name: "Warehouse Name" },
    { id: "reservedStock", name: "Reserved Stocks" },
    { id: "totalStock", name: "Total Stocks" },
    { id: "erplyCurrentStockValue", name: "Available Stocks" },
];

const auditColumns = [
    { id: "sn", name: "SN" },
    { id: "log", name: "Log" },
    { id: "date", name: "Date" },
    { id: "actionBy", name: "Action By" },
];

const ViewOrderDetail = (props) => {
    const theme = useTheme();
    const [value, setValue] = useState(0);
    const [open, setOpen] = useState(false);
    const [message, setMessage] = useState("");
    const [messageState, setMessageState] = useState("");
    const [togglerLanding, setTogglerLanding] = useState(false);
    const [shippitLoading, setShippitLoading] = useState(false);
    const [imgIndex1, setImgIndex1] = useState(0);
    const [sohDetails, setSohDetails] = useState("");
    const [orderNumber, setOrderNumber] = useState("");
    const [auditValues, setAuditValues] = useState("");
    const [auditLoading, setAuditLoading] = useState(false);
    const [assignLoading, setAssignLoading] = useState(false);
    const [branchState, setBranchState] = useState({
        orderBranch: "",
        orderBranchSet: "",
        lineBranchID: "",
        orderProductID: "",
        // lineBranchSet: []
    });
    const [branchLoading, setBranchLoading] = useState(false);
    const [showOrderBranchChange, setShowOrderBranchChange] = useState(false);
    const [multipleStockName, setMultipleStockName] = useState("");
    const [stockLists, setStockLists] = useState([]);
    const { getTokenRefreshed: refresh, open: tokenOpen, overlay: overlay, setOpen: setTokenOpen, message: tokenMessage, messageState: tokenMessageState } = useTokenRefresh();

    const handleChange = (event, newValue) => {
        setValue(newValue);
    };
    const handleView = (row) => {
        setSohDetails(row);
    };

    const handleBack = () => {
        setSohDetails("");
    }

    const handleBackLog = () => {
        setAuditValues("");
    }

    const [dialogDetails, setDialogDetails] = useState({
        open: true,
    });

    useEffect(() => {
        props.sendDetails(dialogDetails);
        props.viewDetails?.shopify_order_items?.map((order, index) => {
            if (index === 0) {
                var newStocks = order.stock_on_hand?.soh;
                setStockLists(newStocks);
            }
        });
    }, [props, dialogDetails]);

    useEffect(() => {
        var allStock = [];
        if (props.viewDetails.shopify_order_items) {
            props.viewDetails.shopify_order_items.map(
                (product, index) =>
                    product.assign_to_branch !== null &&
                    product.assign_to_branch?.map((sto, index2) => {
                        allStock.push(sto);
                    })
            );

            if (allStock.length === 1) {
                setMultipleStockName(allStock[0].branch?.name);
            }

            if (allStock.length > 1) {

                allStock.map((stock, index, arr) => {
                    const prev = arr[index - 1];
                    if (prev?.branch?.warehouseID && stock?.branch?.warehouseID !== prev.branch?.warehouseID) {
                        setMultipleStockName("Multiple Branch");
                    } else {
                        setMultipleStockName(stock?.branch?.name);
                    }
                });
            }
        }
    }, [props.viewDetails.shopify_order_items]);

    const handleRefetch = () => {
        setDialogDetails({
            open: true,
            refetch: true,
            id: props.viewDetails.id,
        });
        setTimeout(() => {
            setDialogDetails({
                open: true,
                refetch: false,
                id: "",
            });
        }, 100);
    };

    const handleImageTogglerLanding = (index) => {
        setImgIndex1(index);
        setTogglerLanding((prev) => !prev);
    };

    const handleClose = () => {
        setDialogDetails({
            ...dialogDetails,
            open: false,
        });
    };

    const displayText = (descriptionTexts) => {
        const textIsEmpty = descriptionTexts === null || descriptionTexts === "";
        return !textIsEmpty ? (
            parse(descriptionTexts)
        ) : (
            "-"
        );
    }

    const handleAssignOrder = () => {
        setAssignLoading(true);
        const orderNumber = props.viewDetails.order_number.replace(/^#/, '');
        httpclient
            .post(`shopify/order-assign`,
                {
                    order_number: orderNumber,
                }
            )
            .then(({ data }) => {
                if (data.status === 200) {
                    setAssignLoading(false);
                    setOpen(true);
                    setMessageState("success");
                    setMessage(data.message);

                    //Refetching the data
                    handleRefetch();
                } else {
                    setAssignLoading(false);
                    setOpen(true);
                    setMessageState("error");
                    setMessage(data.error || data.message);
                }
            }).catch((err) => {
                if (err.response.status === 401) {
                    refresh();
                    setOpen(tokenOpen);
                    setMessage(tokenMessage);
                    setMessageState("error");
                } else if (err.response.status === 422) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setAssignLoading(false);
                } else if (err.response.status === 400) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setAssignLoading(false);

                } else {
                    setOpen(true);
                    setMessage(err.response.data.message);
                    setMessageState("error");
                    setAssignLoading(false);
                }
            })
    };


    const handlePushToShippit = () => {
        setShippitLoading(true);
        httpclient
            .post(`starshipit/create-order`,
                {
                    order_id: props.viewDetails.id,
                }
            )
            .then(({ data }) => {
                if (data.status === 200) {
                    setShippitLoading(false);
                    setOpen(true);
                    setMessageState("success");
                    setMessage(data.message);

                    //Refetching the data
                    handleRefetch();
                } else {
                    setShippitLoading(false);
                    setOpen(true);
                    setMessageState("error");
                    setMessage(data.error || data.message);
                }
            }).catch((err) => {
                if (err.response.status === 401) {
                    refresh();
                    setOpen(tokenOpen);
                    setMessage(tokenMessage);
                    setMessageState("error");
                } else if (err.response.status === 422) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setShippitLoading(false);
                } else if (err.response.status === 400) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setShippitLoading(false);

                } else {
                    setOpen(true);
                    setMessage(err.response.data.message);
                    setMessageState("error");
                    setShippitLoading(false);
                }
            })
    };

    const handleViewAuditTrial = () => {
        setAuditLoading(true);
        const orderNumber = props.viewDetails.order_number.replace(/^#/, '');
        httpclient(
            `request-response?requestName=shopify/order-log/${orderNumber}`
        ).then(({ data }) => {
            if (data.length > 0) {
                setAuditValues(data);
                setAuditLoading(false);
            }
            else {
                setAuditLoading(false);
                setOpen(true);
                setMessageState("error");
                setMessage(data.error || data.message);
            }
        }).catch((err) => {
            if (err.response.status === 401) {
                refresh();
                setOpen(tokenOpen);
                setMessage(tokenMessage);
                setMessageState("error");
            } else if (err.response.status === 422) {
                const errorMessages = Object.values(err.response.data.errors).flat();
                setOpen(true);
                setMessage(errorMessages);
                setMessageState("error");
                setAuditLoading(false);
            } else if (err.response.status === 400) {
                const errorMessages = Object.values(err.response.data.errors).flat();
                setOpen(true);
                setMessage(errorMessages);
                setMessageState("error");
                setAuditLoading(false);

            } else {
                setOpen(true);
                setMessage(err.response.data.message);
                setMessageState("error");
                setAuditLoading(false);
            }
        })
    };

    const handleChangeOrderBranch = (e) => {
        var newValue = "";
        setBranchState({
            ...branchState,
            orderBranch: e.target.value,
            orderBranchSet: newValue,
        });
    };

    const changeOrderBranch = () => {
        setBranchLoading(true);
        httpclient
            .post(`shopify/change-order-location`, {
                order_id: props.viewDetails.shopify_order_id,
                branchId: branchState.orderBranch,
            })
            .then(({ data }) => {
                if (data.status === 200) {
                    setBranchLoading(false);
                    setOpen(true);
                    setMessageState("success");
                    setMessage(data.message);
                    setSohDetails("");
                    setShowOrderBranchChange(false);
                    setTimeout(() => {
                        handleRefetch();
                    }, 1000);
                } else {
                    setBranchLoading(false);
                    setShowOrderBranchChange(false);
                    setOpen(true);
                    setMessageState("error");
                    setMessage(data.message);
                }
            })
            .catch((err) => {
                setSohDetails("");
                setShowOrderBranchChange(false);
                if (err.response.status === 401) {
                    refresh();
                    setOpen(tokenOpen);
                    setMessage(tokenMessage);
                    setMessageState("error");
                } else if (err.response.status === 422) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setBranchLoading(false);
                } else if (err.response.status === 400) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setBranchLoading(false);
                } else {
                    setOpen(true);
                    setMessage(err.response.data.message);
                    setMessageState("error");
                    setBranchLoading(false);
                }
            })
    }

    const handleCloseSnack = (event, reason) => {
        if (reason === "clickaway") {
            return;
        }
        setOpen(false);
        setTokenOpen(false);
    };

    return (
        <div>
            <Dialog
                open={dialogDetails.open}
                onClose={handleClose}
                maxWidth="xl"
                fullWidth
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <StyledHeaderTitle id="alert-dialog-title">
                    <div>
                        View Order Details{" "}
                        {"(" +
                            //   (props.viewDetails.handle || "-") +
                            //   "/" +
                            (props.viewDetails.order_number || "-") +
                            ")"}
                    </div>
                    <IconButton onClick={handleClose}>
                        <Close />
                    </IconButton>
                </StyledHeaderTitle>
                {props.singleLoading ? (
                    <DialogContent>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                                <Box p={3} sx={{ width: "100%" }}>
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                </Box>
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <Box p={3} sx={{ width: "100%" }}>
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                </Box>
                            </Grid>
                        </Grid>
                    </DialogContent>
                ) : (
                    <DialogContent sx={{ padding: "0" }}>
                        <AppBarTabs position="static">
                            <Tabs
                                value={value}
                                onChange={handleChange}
                                indicatorColor="secondary"
                                textColor="inherit"
                                variant="fullWidth"
                                aria-label="full width tabs example"
                            >
                                <Tab label="Details" {...a11yProps(0)} />
                                <Tab label="Order Items" {...a11yProps(1)} />
                                <Tab label="Delivery Details" {...a11yProps(2)} />
                                <Tab label="Customer Details" {...a11yProps(3)} />

                            </Tabs>
                        </AppBarTabs>

                        <TabPanel value={value} index={0} dir={theme.direction}>
                            {auditValues ? (
                                <>
                                    <Box display={"flex"} justifyContent={"space-between"}>
                                        <h3>Audit Trail</h3>
                                        <Button onClick={handleBackLog}>
                                            <KeyboardArrowLeft
                                                fontSize="small"
                                                sx={{ marginRight: "5px" }}
                                            />{" "}
                                            <span>Back</span>
                                        </Button>
                                    </Box>

                                    <BasicTable columns={auditColumns} rows={auditValues} />
                                </>
                            ) : (
                                <Box>
                                    <Grid container spacing={2}>
                                        {/* Left Side */}
                                        <Grid item xs={12} md={6}>
                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>Order Number</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values>{props.viewDetails.order_number || "-"}</Values>
                                            </FlexContent>

                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>Fulfillment Status</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values>{props.viewDetails.fullfillment_status || "-"}</Values>
                                            </FlexContent>

                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>Shipping Method</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values>
                                                    {props.viewDetails.shipping_method || "-"}
                                                </Values>
                                            </FlexContent>
                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>Finiancial Status</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values>{props.viewDetails.financial_status || "-"}</Values>
                                            </FlexContent>
                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>Payment Gateway</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values>{displayText(props.viewDetails.payment_gateway_names)}</Values>
                                            </FlexContent>
                                            {props.viewDetails.checkout_method === "pickup" &&
                                                <FlexContent>
                                                    <FlexInnerTitle>
                                                        <span>Pickup Location</span> <span> : </span>
                                                    </FlexInnerTitle>
                                                    <Values>{props.viewDetails.pick_up_location}</Values>
                                                </FlexContent>
                                            }
                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>Assigned to Branch</span> <span> : </span>
                                                </FlexInnerTitle>
                                                {/* <Values>{props.viewDetails.assign_to_branch?.name || "-"}</Values> */}
                                                {!showOrderBranchChange ? (
                                                    <Box ml={2}>
                                                        {/* {props.viewDetails.assign_to_branch?.ciBranchName} */}
                                                        {multipleStockName}
                                                        {props.viewDetails.assign_to_branch && props.viewDetails.orderLocationChangeEnabled === 1 ? (
                                                            <IconButton
                                                                onClick={() => setShowOrderBranchChange(true)}
                                                            >
                                                                <Edit fontSize="small" color="primary" />
                                                            </IconButton>
                                                        ) : null}
                                                    </Box>
                                                ) : (
                                                    <Values>
                                                        <FormControl
                                                            style={{ width: "180px", marginBottom: "10px" }}
                                                        >
                                                            <Select
                                                                value={
                                                                    branchState.orderBranch
                                                                        ? branchState.orderBranch
                                                                        : props.viewDetails.assign_to_branch?.warehouseID
                                                                }
                                                                onChange={(e) => handleChangeOrderBranch(e)}
                                                                name="orderBranch"
                                                            >
                                                                <MenuItem value={""}>
                                                                    <em>Select Branch</em>
                                                                </MenuItem>
                                                                {stockLists.length > 0 &&
                                                                    stockLists.map((stock) => (

                                                                        <MenuItem
                                                                            value={stock?.warehouseID}
                                                                            key={stock?.warehouseID}
                                                                        >
                                                                            {stock?.name}
                                                                        </MenuItem>
                                                                    ))}
                                                            </Select>
                                                        </FormControl>

                                                        {branchLoading ? (
                                                            <CircularProgress
                                                                style={{
                                                                    height: "25px",
                                                                    width: "25px",
                                                                    marginLeft: "10px",
                                                                    position: "relative",
                                                                    top: "10px",
                                                                }}
                                                            />
                                                        ) : (
                                                            <>
                                                                <Tooltip title="Back">
                                                                    <Button
                                                                        variant="contained"
                                                                        color="primary"
                                                                        onClick={() =>
                                                                            setShowOrderBranchChange(false)
                                                                        }
                                                                        sx={{ marginLeft: "10px" }}
                                                                    >
                                                                        <ArrowBack fontSize="small" />
                                                                    </Button>
                                                                </Tooltip>

                                                                <Tooltip title="Change Branch">
                                                                    <Button
                                                                        variant="contained"
                                                                        color="primary"
                                                                        onClick={changeOrderBranch}
                                                                        sx={{ marginLeft: "10px" }}
                                                                    >
                                                                        <Done fontSize="small" />
                                                                    </Button>
                                                                </Tooltip>
                                                            </>
                                                        )}
                                                    </Values>
                                                )}
                                            </FlexContent>
                                        </Grid>

                                        {/* Left Side */}

                                        {/* Right Side */}
                                        <Grid item xs={12} md={6}>
                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>Pending Process</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values>{props.viewDetails.pendingProcess === "1" ? <Check color="primary" /> : <Clear color="error" />}</Values>
                                            </FlexContent>

                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>Total Items</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values>
                                                    {props.viewDetails.total_items}
                                                </Values>
                                            </FlexContent>

                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>Total Price(Shipping Inc.)</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values>
                                                    ${props.viewDetails.total_price}
                                                </Values>
                                            </FlexContent>

                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>Created Date</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values>
                                                    {moment(props.viewDetails.order_created_at).format(
                                                        "ddd, DD MMM YYYY, h:mm a"
                                                    ) || "-"}
                                                </Values>
                                            </FlexContent>

                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>Last Modified Date</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values>
                                                    {moment(props.viewDetails.order_updated_at).format(
                                                        "ddd, DD MMM YYYY, h:mm a"
                                                    ) || "-"}
                                                </Values>
                                            </FlexContent>

                                            {props.viewDetails.orderAssignedEnabled === 1 &&
                                                <FlexContent>
                                                    <FlexInnerTitle></FlexInnerTitle>
                                                    <Values
                                                        style={{
                                                            display: "flex",
                                                            justifyContent: "flex-end",
                                                            width: "100%",
                                                        }}
                                                    >
                                                        <Button
                                                            size="small"
                                                            variant="contained"
                                                            onClick={handleAssignOrder}
                                                            disabled={assignLoading}
                                                        >
                                                            {"Assign Order"}{" "}
                                                            <MultipleStop
                                                                fontSize="small"
                                                                style={{ marginLeft: "5px" }}
                                                            />{" "}

                                                        </Button>
                                                    </Values>
                                                </FlexContent>
                                            }

                                            {props.viewDetails.orderPushToShipitEnabled === 1 &&
                                                <FlexContent>
                                                    <FlexInnerTitle></FlexInnerTitle>
                                                    <Values
                                                        style={{
                                                            display: "flex",
                                                            justifyContent: "flex-end",
                                                            width: "100%",
                                                        }}
                                                    >
                                                        <Button
                                                            size="small"
                                                            variant="contained"
                                                            onClick={handlePushToShippit}
                                                            disabled={shippitLoading}
                                                        >
                                                            {props.viewDetails.assign_to_branch ? `Push To ${props.viewDetails.assign_to_branch?.name}` : `Push To Shipit`}{" "}
                                                            <ArrowForward
                                                                fontSize="small"
                                                                style={{ marginLeft: "5px" }}
                                                            />{" "}

                                                        </Button>
                                                    </Values>
                                                </FlexContent>
                                            }

                                            <FlexContent>
                                                <FlexInnerTitle></FlexInnerTitle>
                                                <Values
                                                    style={{
                                                        display: "flex",
                                                        justifyContent: "flex-end",
                                                        width: "100%",
                                                    }}
                                                >
                                                    <Button
                                                        size="small"
                                                        variant="contained"
                                                        disabled={auditLoading}
                                                        onClick={handleViewAuditTrial}
                                                    >
                                                        <LockOpen
                                                            fontSize="small"
                                                            style={{ marginRight: "5px" }}
                                                        />{" "}
                                                        View Audit Trail
                                                    </Button>
                                                </Values>
                                            </FlexContent>

                                        </Grid>
                                        {/* Right Side */}

                                        <Grid item xs={12}>
                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>Notes</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values>
                                                    {/* {props.viewDetails.shortDescription || "-"} */}
                                                    {displayText(props.viewDetails.note)}
                                                </Values>
                                            </FlexContent>
                                        </Grid>
                                    </Grid>
                                </Box>
                            )}
                        </TabPanel>

                        <TabPanel value={value} index={1} dir={theme.direction}>
                            {sohDetails ? (
                                <>
                                    <Box display={"flex"} justifyContent={"space-between"}>
                                        <h3>SOH Details</h3>
                                        <Button onClick={handleBack}>
                                            <KeyboardArrowLeft fontSize="small" sx={{ marginRight: "5px" }} />
                                            <span>Back</span>
                                        </Button>
                                    </Box>
                                    <BasicTable
                                        columns={sohColumns}
                                        rows={sohDetails.stock_on_hand?.soh}
                                    />
                                </>
                            ) : (

                                <BasicTableShopify
                                    columns={orderColumns}
                                    rows={props.viewDetails.shopify_order_items}
                                    handleView={handleView}
                                    handleRefetch={handleRefetch}
                                />
                            )}

                        </TabPanel>



                        <TabPanel value={value} index={2} dir={theme.direction}>

                            <Box>
                                <Grid container>
                                    <Grid xs={12} md={6}>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Delivered To</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.shopify_order_deliveries?.first_name || "-"}{" "}{props.viewDetails.shopify_order_deliveries?.last_name || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Email</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.shopify_order_deliveries?.email || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Phone</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                {props.viewDetails.shopify_order_deliveries?.phone || "-"}
                                            </Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Delivery Address</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{[props.viewDetails.shopify_order_deliveries?.street, props.viewDetails.shopify_order_deliveries?.city, props.viewDetails.shopify_order_deliveries?.suburb, props.viewDetails.shopify_order_deliveries?.post_code, props.viewDetails.shopify_order_deliveries?.state, props.viewDetails.shopify_order_deliveries?.country].filter(Boolean).join(', ')}</Values>

                                        </FlexContent>

                                    </Grid>
                                </Grid>
                            </Box>
                        </TabPanel>
                        {/* <Box
                                            pl={3}
                                            ml={3}
                                            style={{ borderLeft: "1px solid #999", height: "100%" }}
                                        >
                                            <h3>
                                                Customer Details:{" "}
                                            </h3>
                                            </Box> */}
                        <TabPanel value={value} index={3} dir={theme.direction}>

                            <Box>
                                <Grid container>
                                    <Grid item xs={12} md={6}>

                                        {props.loginValue?.company_name !== "Substance Wholesale" &&
                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>ERPLY Customer ID</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values>{props.viewDetails.shopify_customer?.erplyCustomerID || "-"}</Values>
                                            </FlexContent>
                                        }

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Customer Name</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.shopify_customer?.display_name || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Customer Address</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{[props.viewDetails.shopify_customer?.address1, props.viewDetails.shopify_customer?.city, props.viewDetails.shopify_customer?.zipcode, props.viewDetails.shopify_customer?.province, props.viewDetails.shopify_customer?.country].filter(Boolean).join(', ')}</Values>

                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Email</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.shopify_customer?.email || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Phone</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                {props.viewDetails.shopify_customer?.phone || "-"}
                                            </Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Company</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                {props.viewDetails.shopify_customer?.company || "-"}
                                            </Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Customer State</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                {props.viewDetails.shopify_customer?.state || "-"}
                                            </Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Email Marketing Consent</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                {(props.viewDetails.shopify_customer?.emailMarketingConsent || "-").replaceAll("_", " ")}
                                            </Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>SMS Marketing Consent</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                {(props.viewDetails.shopify_customer?.smsMarketingConsent || "-").replaceAll("_", " ")}
                                            </Values>
                                        </FlexContent>



                                    </Grid>
                                </Grid>
                            </Box>

                        </TabPanel>


                    </DialogContent>
                )}
                <DialogActions>
                    <Button onClick={handleClose} variant="outlined" color="primary">
                        Close
                    </Button>
                </DialogActions>
            </Dialog>

            {/* {openPolicyDialog && (
        <ViewPolicyDialog
          singleLoading={singleLoading}
          viewDetails={viewDetails}
          sendDetails={sendDetails}
        />
      )} */}

            <Snackbar
                autoHideDuration={3000}
                anchorOrigin={{ vertical: "top", horizontal: "right" }}
                open={open || tokenOpen}
                onClose={handleCloseSnack}
            >
                <Alert
                    onClose={handleCloseSnack}
                    severity={messageState || tokenMessageState}
                    sx={{ width: "100%" }}
                >
                    {message || tokenMessage}
                </Alert>
            </Snackbar>
        </div>
    );
};

export default ViewOrderDetail;
