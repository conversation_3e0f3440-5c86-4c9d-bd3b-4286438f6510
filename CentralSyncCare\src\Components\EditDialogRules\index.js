import { DemoContainer } from '@mui/x-date-pickers/internals/demo';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';

import {
    Box,
    Button,
    Checkbox,
    CircularProgress,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    FormControl,
    FormControlLabel,
    FormGroup,
    FormHelperText,
    Grid,
    InputLabel,
    MenuItem,
    Radio,
    RadioGroup,
    Select,
    styled,
    Switch,
    TextField,
} from "@mui/material";
import { useEffect, useState } from "react";
import * as React from 'react';
import dayjs from 'dayjs';
import InputDetails from "../InputDetails";
import OutputDetails from "../OutputDetails";
import httpclient from "../../Utils";
import { useMemo } from 'react';


const StyledHeaderTitle = styled(DialogTitle)(({ theme }) => ({
    background: theme.palette.primary.main,
    color: "#fff",
    position: "relative",
    "& button": {
        position: "absolute",
        right: "15px",
        top: "15px",
        color: "#fff",
    },
}));

const EditDialogRules = (props) => {
    const [dialogDetails, setDialogDetails] = useState({
        open: true,
        success: false,
    });

    const [startDateValue, setStartDateValue] = useState("");
    const [endDateValue, setEndDateValue] = useState("");
    const [formData, setFormData] = useState({
        name: props.viewDetails.name ? props.viewDetails.name : '',
        template: props.viewDetails.template ? props.viewDetails.template : '',
        notify_to_customer: props.viewDetails.notify_to_customer ? props.viewDetails.notify_to_customer : false,
        start_date: props.viewDetails.start_date ? props.viewDetails.start_date : "",
        end_date: props.viewDetails.end_date ? props.viewDetails.end_date : "",
        customer_point_required: props.viewDetails.customer_point_required ? props.viewDetails.customer_point_required : "",
        customer_point_used_type: props.viewDetails.customer_point_used_type ? props.viewDetails.customer_point_used_type : "",
        is_active: props.viewDetails.is_active ? props.viewDetails.is_active : 0,
        is_used_one_time: props.viewDetails.is_used_one_time ? props.viewDetails.is_used_one_time : 0,
        is_customer_point_required: props.viewDetails.is_customer_point_required ? props.viewDetails.is_customer_point_required : 0,
        rule_type: props.viewDetails.rule_type ? props.viewDetails.rule_type : "loyalty",
        input_output_code: props.viewDetails.input_output_code ? props.viewDetails.input_output_code : "",
        input_detail: [],
        output_detail: [],
        store_id: []

    });

    const [pointUsedType, setPointUsedType] = useState([]);
    const [platformList, setPlatformList] = useState([]);
    const [inputData, setInputData] = useState([]);
    const [outputData, setOutputData] = useState([]);
    const [productList, setProductList] = useState([]);
    const [productListInput, setProductListInput] = useState([]);
    const [storeList, setStoreList] = useState([]);
    const [productCategoryList, setProductCategoryList] = useState([]);
    const [pointUsedTypeListLoading, setPointUsedTypeListLoading] = useState(false);
    const [platformListLoading, setPlatformListLoading] = useState(false);
    const [inputListLoading, setInputListLoading] = useState(false);
    const [outputListLoading, setOutputListLoading] = useState(false);
    const [productListLoading, setProductListLoading] = useState(false);
    const [productListInputLoading, setProductListInputLoading] = useState(false);
    const [storeListLoading, setStoreListLoading] = useState(false);
    const [productCategoryLoading, setProductCategoryLoading] = useState(false);
    const [code, setCode] = useState("");

    const [inputDetails, setInputDetails] = useState([
        { input_output_id: 1, value: "", condition: "and" },
    ]);

    const [outputDetails, setOutputDetails] = useState([
        { input_output_id: "", value: "", condition: "and" },
    ]);

    const [validationErrors, setValidationErrors] = useState({});

    useEffect(() => {
        getInputList();
        getPlatformList();
        getStoreList();
        //getOutputList();
    }, []);

    useEffect(() => {
        const lists = inputDetails.map((a) => a.input_output_id)
        getOutputListByInput(lists);
    }, [inputDetails]);

    // useEffect(() => {
    //     const lists = inputDetails.find((a) => a.input_output_id === 16)
    //     if (lists) {
    //         getProductCategoryList();
    //         getProductListFilterInput();
    //     }
    // }, [inputDetails.find((a) => a.input_output_id === 16]);
    const listItem = useMemo(() => inputDetails.find((a) => a.input_output_id === 16), [inputDetails]);
    const listItemOutput = useMemo(() => outputDetails.find((a) => a.input_output_id === 12), [outputDetails]);
    
    useEffect(() => {
        if (listItem) {
            getProductCategoryList();
            getProductListFilterInput();
        }
    }, [listItem]);


    useEffect(() => {
        if (listItemOutput) {
            getProductList();
            getProductCategoryList();
        }
    }, [listItemOutput]);

    useEffect(() => {
        if (formData.is_used_one_time == 1) {
            getPointUsedTypeList();
        }
    }, [formData.is_used_one_time]);

    const getPointUsedTypeList = () => {
        setPointUsedTypeListLoading(true);
        httpclient.get(`https://synccare.io/loyalty-manager/public/api/v1/lightspeed/loyalty/point-used-type-list`).then(({ data }) => {
            if (data.status === 200) {
                setPointUsedType(data.data);
                setPointUsedTypeListLoading(false);
            } else {
                setPointUsedTypeListLoading(false);
            }
        })
    };

    const getPlatformList = () => {
        setPlatformListLoading(true);
        httpclient.get(`https://synccare.io/loyalty-manager/public/api/v1/lightspeed/loyalty/platform-list`).then(({ data }) => {
            if (data.status === 200) {
                setPlatformList(data.data);
                setPlatformListLoading(false);
            } else {
                setPlatformListLoading(false);
            }
        })
    };

    const getStoreList = () => {
        setStoreListLoading(true);
        httpclient.get(`https://synccare.io/loyalty-manager/public/api/v1/lightspeed/loyalty/store-list`).then(({ data }) => {
            if (data.status === 200) {
                setStoreList(data.data);
                setStoreListLoading(false);
            } else {
                setStoreListLoading(false);
            }
        })
    };

    const getInputList = () => {
        setInputListLoading(true);
        httpclient.get(`https://synccare.io/loyalty-manager/public/api/v1/lightspeed/loyalty/input-list`).then(({ data }) => {
            if (data.status === 200) {
                setInputData(data.data);
                setInputListLoading(false);
            } else {
                setInputListLoading(false);
            }
        })
    };

    const getOutputList = () => {
        setOutputListLoading(true);
        httpclient.get(`request-response?requestName=lightspeed/loyalty/output-list`).then(({ data }) => {
            if (data.status === 200) {
                setOutputData(data.data);
                setOutputListLoading(false);
            } else {
                setOutputListLoading(false);
            }
        })
    };

    const getOutputListByInput = (id) => {
        if (id.length > 0) {
            setOutputListLoading(true);
            // httpclient.get(`request-response?requestName=lightspeed/loyalty/output-list-by-input/${id}`)
            httpclient.post(`https://synccare.io/loyalty-manager/public/api/v1/lightspeed/loyalty/output-list-by-input`, {
                input_id: id
            }).then(({ data }) => {
                if (data.status === 200) {
                    setOutputData(data.data);
                    setOutputListLoading(false);
                } else {
                    setOutputListLoading(false);
                }
            })
        }
    };

    const getProductList = () => {
        setProductListLoading(true);
        httpclient.get(`https://synccare.io/loyalty-manager/public/api/v1/lightspeed/loyalty/product-list`).then(({ data }) => {
            if (data.status === 200) {
                setProductList(data.data);
                setProductListLoading(false);
            } else {
                setProductListLoading(false);
            }
        })
    };

    // const getProductListFilter = (categoryIds) => {
    //     setProductListLoading(true);
    //     httpclient.get(`https://synccare.io/loyalty-manager/public/api/v1/lightspeed/loyalty/product-list?category_id=${categoryIds}`).then(({ data }) => {
    //         if (data.status === 200) {
    //             setProductList(data.data);
    //             setProductListLoading(false);
    //         } else {
    //             setProductListLoading(false);
    //         }
    //     })
    // };

    const getProductListFilter = (categoryIds) => {
        setProductListLoading(true);

        httpclient.post(`https://synccare.io/loyalty-manager/public/api/v1/lightspeed/loyalty/product-list`, {
            category_id: categoryIds
        })
            .then(({ data }) => {
                if (data.status === 200) {
                    setProductList(data.data);
                    setProductListLoading(false);
                } else {
                    setProductListLoading(false);
                }
            })
            .catch(() => setProductListLoading(false));
    };

    const getProductListFilterInput = (categoryIds) => {
        setProductListInputLoading(true);

        httpclient.post(`https://synccare.io/loyalty-manager/public/api/v1/lightspeed/loyalty/product-list`, {
            category_id: categoryIds
        })
            .then(({ data }) => {
                if (data.status === 200) {
                    setProductListInput(data.data);
                    setProductListInputLoading(false);
                } else {
                    setProductListInputLoading(false);
                }
            })
            .catch(() => setProductListInputLoading(false));
    };


    const getProductCategoryList = () => {
        setProductListLoading(true);
        httpclient.get(`https://synccare.io/loyalty-manager/public/api/v1/lightspeed/loyalty/product-category-list`).then(({ data }) => {
            if (data.status === 200) {
                setProductCategoryList(data.data);
                setProductCategoryLoading(false);
            } else {
                setProductCategoryLoading(false);
            }
        })
    };
    const handleSwitch = (event) => {
        if (!props.view) {
            const isChecked = event.target.checked;
            setFormData({ ...formData, is_active: isChecked === true ? 1 : 0 });
        }
    };

    const handleChangeNotify = (event) => {
        if (!props.view) {
            setFormData({
                ...formData,
                notify_to_customer: event.target.checked
            })
        }
    };

    const handleChangeRadioUsedTime = (event) => {
        if (!props.view) {
            setFormData({
                ...formData,
                customer_point_used_type: "",
                is_used_one_time: event.target.value
            })
        }
    };
    const handleChangeRadioRuleType = (event) => {
        if (!props.view) {
            setFormData({
                ...formData,
                customer_point_used_type: "",
                rule_type: event.target.value
            })
        }
    };

    const handleChangeRadioCustomerPoints = (event) => {
        if (!props.view) {
            setFormData({
                ...formData,
                customer_point_required: "",
                is_customer_point_required: event.target.value
            })
        }
    };

    useEffect(() => {
        if (startDateValue !== "") {
            const formattedStartDate = dayjs(startDateValue).format("YYYY-MM-DD HH:mm");
            setFormData({
                ...formData,
                start_date: formattedStartDate
            })
        }
        if (endDateValue !== "") {
            const formattedEndDate = dayjs(endDateValue).format("YYYY-MM-DD HH:mm");
            setFormData({
                ...formData,
                end_date: formattedEndDate
            })
        }
        if (code !== "") {
            setFormData({
                ...formData,
                input_output_code: code
            })
        }
    }, [startDateValue, endDateValue, code]);


    useEffect(() => {
        if (props.viewDetails.inputDetails) {
            const newDetails = props.viewDetails.inputDetails.map((detail) => ({
                input_output_id: detail.id || "",
                value: detail.inputDetailValue
                    ? detail.inputDetailValue.length > 0
                        ? detail.inputDetailValue.map((item) => item.value)
                        : detail.inputDetailValue[0]?.value || ""
                    : "",
                condition: detail.condition || "and",
            }));
            setInputDetails([...newDetails]);
        }
        if (props.viewDetails.outputDetails) {
            const newDetails = props.viewDetails.outputDetails.map((detail) => ({
                input_output_id: detail.id || "",
                value: detail.outputDetailValue
                    ? detail.outputDetailValue.length > 0
                        ? detail.outputDetailValue.map((item) => item.value)
                        : detail.outputDetailValue[0]?.value || ""
                    : "",
                condition: detail.condition || "and",
            }));
            setOutputDetails([...newDetails]);
        }
        if (props.viewDetails.storeList) {
            const sendTypeIds = props.viewDetails.storeList.map(item => item.value);

            setFormData({
                ...formData,
                store_id: sendTypeIds,
            });
        }

    }, [props.viewDetails]);



    useEffect(() => {
        props.sendEdit(dialogDetails, formData);
    }, [dialogDetails]);


    const handleClose = () => {
        setDialogDetails({
            ...dialogDetails,
            open: false,
        });
    };

    const handleYes = () => {

        let errors = {};

        if (!formData.name.trim()) {
            errors.name = "Name is required.";
        }
        if (formData.input_detail[0].input_output_id === "") {
            errors.input_detail = "At least one input is required.";
        }
        if (formData.store_id.length === 0) {
            errors.store_id = "At least one store is required.";
        }

        if (Object.keys(errors).length > 0) {
            setValidationErrors(errors);
            return;
        }
        setDialogDetails({
            ...dialogDetails,
            open: false,
            success: true,
        });

    };

    const handleChange = (e) => {
        if (!props.view) {
            const { name, value } = e.target;
            setFormData((prevData) => ({
                ...prevData,
                [name]: value,
            }));
            validateField(name, value);
        }
    };

    /////----------Input----------///




    const addInputDetail = () => {
        if (!props.view) {
            setInputDetails([
                ...inputDetails,
                { input_output_id: "", value: "", condition: "and" },
            ]);
        }
    };


    const removeInputDetail = (index) => {
        if (!props.view) {
            const updatedInputs = [...inputDetails];
            updatedInputs.splice(index, 1);
            setInputDetails(updatedInputs);
        }
    };

    const updateInputDetail = (index, field, newValue) => {

        const updatedInputs = [...inputDetails];
        updatedInputs[index][field] = Array.isArray(newValue) ? [...newValue] : newValue;
        setInputDetails(updatedInputs);

    };

    const isAddButtonDisabled = inputDetails.some((detail) => {
        if (!detail.input_output_id) return true;
        const selectedInput = inputData.find(
            (input) => input.id === parseInt(detail.input_output_id)
        );
        return selectedInput?.is_value_required && !detail.value;
    });

    ////------------Input---------////

    ////-----------Output---------///



    const addOutputDetail = () => {
        if (!props.view) {
            setOutputDetails([
                ...outputDetails,
                { input_output_id: "", value: "", condition: "and" },
            ]);
        }
    };

    const removeOutputDetail = (index) => {
        if (!props.view) {
            const updatedOutputs = [...outputDetails];
            updatedOutputs.splice(index, 1);
            setOutputDetails(updatedOutputs);
        }
    };

    const updateOutputDetail = (index, field, newValue) => {
        if (!props.view) {
            const updatedOutputs = [...outputDetails];
            updatedOutputs[index][field] = newValue;
            setOutputDetails(updatedOutputs);
        }
    };

    const isAddButtonDisabledOutput = outputDetails.some((detail) => {
        if (!detail.input_output_id) return true;
        const selectedOutput = outputData.find(
            (output) => output.id === parseInt(detail.input_output_id)
        );
        return selectedOutput?.is_value_required && !detail.value;
    });

    ///---------Output---------//////

    useEffect(() => {
        setFormData(prevFormData => ({
            ...prevFormData,
            input_detail: [...inputDetails],
            output_detail: [...outputDetails]
        }));
        validateField("input_detail", [...inputDetails]);

    }, [inputDetails, outputDetails]);

    const validateField = (name, value) => {
        let error = "";

        if (name === "name" && !value.trim()) {
            error = "Name is required.";
        } else if (name === "input_detail" && value[0].input_output_id === "") {
            error = "At least one input is required.";
        }
        else if (name === "store_id" && value.length === 0) {
            error = "At least one store is required.";
        }

        setValidationErrors((prevErrors) => ({
            ...prevErrors,
            [name]: error
        }));
    };
    console.log("formData", formData)
    return (
        <div>
            <Dialog
                open={dialogDetails.open}
                onClose={handleClose}
                maxWidth="lg"
                fullWidth
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <StyledHeaderTitle id="alert-dialog-title">
                    {props.view ? `View Loyalty/Redeem (${props.viewDetails.name})` : props.viewDetails.id ? `Edit Loyalty/Redeem` : `Add Loyalty/Redeem`}
                </StyledHeaderTitle>
                <DialogContent>
                    <Box pt={3}>
                        <Box p={3} sx={{
                            boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)"
                        }}>
                            {!props.view && <h5 style={{ margin: 0 }}><i>{`Fields with * are mandatory`}</i></h5>}
                            <Grid container spacing={2}>
                                <Grid item xs={12} md={9}>


                                    <FormControl sx={{ marginTop: "10px" }} required fullWidth error={!!validationErrors.store_id}>
                                        <InputLabel>{storeListLoading ? "Please Wait.." : `Select Store`}</InputLabel>
                                        <Select
                                            multiple
                                            value={formData.store_id || []}
                                            label={storeListLoading ? "Please Wait.." : `Select Store`}
                                            name="store_id"
                                            onChange={handleChange}
                                            renderValue={(selected) =>
                                                storeList
                                                    ?.filter((item) => selected.includes(item.id))
                                                    .map((item) => item.name)
                                                    .join(", ") || `Select Store`
                                            }
                                        >
                                            <MenuItem value="">
                                                <em>Select Store</em>
                                            </MenuItem>
                                            {storeList &&
                                                storeList.map((point) => (
                                                    <MenuItem key={point.id} value={point.id}>
                                                        <Checkbox checked={formData.store_id?.includes(point.id)} />
                                                        {point.name}
                                                    </MenuItem>
                                                ))}
                                        </Select>

                                        {storeListLoading && (
                                            <CircularProgress
                                                size={24}
                                                style={{
                                                    position: "absolute",
                                                    top: "50%",
                                                    right: 16,
                                                    marginTop: -12,
                                                    marginRight: 10,
                                                }}
                                            />
                                        )}
                                        {!!validationErrors.store_id && (
                                            <FormHelperText>{validationErrors.store_id}</FormHelperText>
                                        )}
                                    </FormControl>

                                </Grid>
                                <Grid item xs={12} md={3}>
                                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: "flex-end" }}>
                                        <span>Active Status</span>
                                        <Switch
                                            checked={formData.is_active === 1 ? true : false}
                                            onChange={handleSwitch}
                                            inputProps={{ 'aria-label': 'controlled' }}
                                        />
                                    </div>

                                </Grid>
                                <Grid item xs={12} md={9}>
                                    <TextField
                                        required
                                        label="Rule Name"
                                        name="name"
                                        value={formData.name}
                                        onChange={handleChange}
                                        fullWidth
                                        margin="normal"
                                        error={!!validationErrors.name}
                                        helperText={validationErrors.name}
                                    />
                                </Grid>
                                <Grid item xs={12} md={6}>
                                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                                        <DemoContainer components={['DateTimePicker']}>
                                            <DateTimePicker
                                                disabled={props.view}
                                                label="Start Date"
                                                name="start_date"
                                                value={formData.start_date ? dayjs(formData.start_date) : null}
                                                onChange={(newValue) =>
                                                    setStartDateValue(newValue)
                                                }

                                            />
                                        </DemoContainer>
                                    </LocalizationProvider>
                                </Grid>
                                <Grid item xs={12} md={6}>

                                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                                        <DemoContainer components={['DateTimePicker']}>
                                            <DateTimePicker
                                                disabled={props.view}
                                                label="End Date"
                                                name="end_date"
                                                value={formData.end_date ? dayjs(formData.end_date) : null}
                                                onChange={(newValue) =>
                                                    setEndDateValue(newValue)
                                                }

                                            />
                                        </DemoContainer>
                                    </LocalizationProvider>
                                </Grid>
                            </Grid>


                            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginTop: "20px" }}>
                                <FormGroup>
                                    <FormControlLabel
                                        control={
                                            <Checkbox
                                                checked={formData.notify_to_customer === 1 || formData.notify_to_customer === true}
                                                onChange={handleChangeNotify}
                                                inputProps={{ 'aria-label': 'controlled' }}
                                            />
                                        }
                                        label="Notify Customer When Redeemed?"
                                    />
                                </FormGroup>
                            </div>
                            {formData.notify_to_customer && (
                                <FormControl fullWidth>
                                    <TextField
                                        required
                                        //label="Notification Message Template"
                                        label="Type message you want to notify customer when this loyalty rule is redeemed."
                                        name="template"
                                        value={formData.template}
                                        onChange={handleChange}
                                        fullWidth
                                        margin="normal"
                                        multiline
                                        rows={3}
                                        helperText="Format: Thank you for your order. You have earned X points"
                                    />
                                </FormControl>
                            )}
                            <Grid container spacing={2}>
                                <Grid item xs={12} md={6}>
                                    <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginTop: "20px" }}>
                                        <h4 style={{ margin: 0, fontWeight: "inherit" }}>Rule Type</h4>
                                        <FormControl>
                                            <RadioGroup
                                                row
                                                aria-labelledby="demo-controlled-radio-buttons-group"
                                                name="controlled-radio-buttons-group"
                                                value={formData.rule_type}
                                                onChange={handleChangeRadioRuleType}
                                            >
                                                <FormControlLabel value="loyalty" control={<Radio />} label="Loyalty" />
                                                <FormControlLabel value="redeemed" control={<Radio />} label="Redeemed" />
                                            </RadioGroup>
                                        </FormControl>

                                    </div>
                                </Grid>
                                <Grid item xs={12} md={6}></Grid>
                                <Grid item xs={12} md={6}>
                                    <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginTop: "20px" }}>
                                        <h4 style={{ margin: 0, fontWeight: "inherit" }}>Redeem Multiple Times?</h4>
                                        <FormControl>
                                            <RadioGroup
                                                row
                                                aria-labelledby="demo-controlled-radio-buttons-group"
                                                name="controlled-radio-buttons-group"
                                                value={formData.is_used_one_time}
                                                onChange={handleChangeRadioUsedTime}
                                            >
                                                <FormControlLabel value="0" control={<Radio />} label="Redeem Once" />
                                                <FormControlLabel value="1" control={<Radio />} label="Yes" />
                                            </RadioGroup>
                                        </FormControl>

                                    </div>
                                </Grid>
                                <Grid item xs={12} md={6}>
                                    {formData.is_used_one_time == "1" &&
                                        <FormControl fullWidth>
                                            <InputLabel>{pointUsedTypeListLoading ? "Please Wait.." : "Redeemed Once A"}</InputLabel>
                                            <Select
                                                value={formData.customer_point_used_type}
                                                label={pointUsedTypeListLoading ? "Please Wait.." : "Redeemed Once A"}
                                                name={"customer_point_used_type"}
                                                onChange={handleChange}
                                            >
                                                <MenuItem value={""}>Select Type</MenuItem>
                                                {pointUsedType && pointUsedType.map((point) => (
                                                    <MenuItem value={point.name}>{point.name}</MenuItem>
                                                ))}
                                            </Select>
                                            {pointUsedTypeListLoading && (
                                                <CircularProgress
                                                    size={24}
                                                    style={{
                                                        position: "absolute",
                                                        top: "50%",
                                                        right: 16,
                                                        marginTop: -12,
                                                        marginRight: 10,
                                                    }}
                                                />
                                            )}
                                        </FormControl>
                                    }
                                </Grid>
                            </Grid>

                            <Grid container spacing={2}>
                                <Grid item xs={12} md={6}>
                                    <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginTop: "20px" }}>
                                        <h4 style={{ margin: 0, fontWeight: "inherit" }}>Minimum Points Required?</h4>
                                        <FormControl>
                                            <RadioGroup
                                                row
                                                aria-labelledby="demo-controlled-radio-buttons-group"
                                                name="controlled-radio-buttons-group"
                                                value={formData.is_customer_point_required}
                                                onChange={handleChangeRadioCustomerPoints}
                                            >
                                                <FormControlLabel value="0" control={<Radio />} label="No" />
                                                <FormControlLabel value="1" control={<Radio />} label="Yes" />
                                            </RadioGroup>
                                        </FormControl>
                                    </div>
                                </Grid>
                                <Grid item xs={12} md={6}>
                                    {formData.is_customer_point_required == "1" &&
                                        <FormControl fullWidth>

                                            <TextField
                                                required
                                                label="Minimum Points"
                                                name="customer_point_required"
                                                value={formData.customer_point_required}
                                                onChange={handleChange}
                                                fullWidth
                                                margin="normal"
                                            />
                                        </FormControl>
                                    }
                                </Grid>
                            </Grid>

                        </Box>

                        <Box p={2} mt={3} sx={{
                            boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)"
                        }}>
                            <InputDetails
                                inputData={inputData}
                                productList={productListInput}
                                productCategoryList={productCategoryList}
                                platformList={platformList}
                                inputDetails={inputDetails}
                                addInputDetail={addInputDetail}
                                updateInputDetail={updateInputDetail}
                                setCode={setCode}
                                code={code}
                                removeInputDetail={removeInputDetail}
                                getProductListFilter={getProductListFilterInput}
                                isAddButtonDisabled={isAddButtonDisabled}
                                platformListLoading={platformListLoading}
                                productListLoading={productListInputLoading}
                                productCategoryLoading={productCategoryLoading}
                                inputListLoading={inputListLoading}
                                validationErrors={validationErrors.input_detail}
                            //view={props.view}
                            />
                        </Box>
                        <Box p={2} mt={3} sx={{
                            boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)"
                        }}>
                            <OutputDetails
                                outputData={outputData}
                                productList={productList}
                                productCategoryList={productCategoryList}
                                outputDetails={outputDetails}
                                addOutputDetail={addOutputDetail}
                                updateOutputDetail={updateOutputDetail}
                                setCode={setCode}
                                code={code}
                                removeOutputDetail={removeOutputDetail}
                                getProductListFilter={getProductListFilter}
                                isAddButtonDisabled={isAddButtonDisabledOutput}
                                productListLoading={productListLoading}
                                productCategoryLoading={productCategoryLoading}
                                outputListLoading={outputListLoading}
                            //view={props.view}
                            />
                        </Box>
                    </Box>
                </DialogContent>
                <DialogActions styled={{ margin: "5px 10px" }}>
                    <Button onClick={handleClose} color="error" variant="contained" autoFocus>
                        {props.view ? "Close" : "Cancel"}
                    </Button>
                    {!props.view &&
                        <Button
                            onClick={handleYes}
                            color="primary"
                            variant="contained"
                            autoFocus
                        >
                            {props.viewDetails.id ? `Edit` : `Save`}
                        </Button>
                    }
                </DialogActions>
            </Dialog>
        </div>
    );
};

export default EditDialogRules;
