"use client";
import Image from "next/image";
import { useEffect, useState } from "react";
import { FilePenLine, Trash2 } from "lucide-react";
import Link from "next/link";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "react-toastify";
import { deleteTestimonial } from "@/actions/testimonial";

const page = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [testimonials, setTestimonials] = useState([]);

  //check authentication
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/");
    }
  }, [status, router]);

  //fetch testimonials
  useEffect(() => {
    const fetchTestimonials = async () => {
      if (!session?.user?.userId) return;

      try {
        const res = await fetch(`/api/testimonial/${session.user.userId}`);
        const data = await res.json();
        setTestimonials(data);
      } catch (err) {
        console.error("Error fetching Testimonials:", err);
      }
    };

    fetchTestimonials();
  }, [session, status]);

  //handle delete testimonial
  const handleDelete = async (testimonialId) => {
    try {
      await deleteTestimonial(testimonialId);
      setTestimonials((prevTestimonials) =>
        prevTestimonials.filter((t) => t._id !== testimonialId)
      );
      toast.success("Testimonial deleted successfully!");
    } catch (error) {
      console.log("Error in handle delete", error);
    }
  };

  //handle edit testimonial
  const handleEdit = (testimonialId) => {
    router.push(`/testimonials/edittestimonial/?id=${testimonialId}`);
  };

  if (status === "loading") {
    return <p>Loading...</p>;
  }

  return (
    <div className="px-4 md:px-8 xl:px-16">
      <div className="flex items-center gap-16 mb-12">
        <h1 className="text-3xl font-semibold">Testimonial Management</h1>
        <Link href={"/testimonials/addtestimonial"}>
          <button className="bg-gray-800 rounded-xl text-white px-4 py-2 hover:bg-gray-900 cursor-pointer">
            Add Testimonial
          </button>
        </Link>
      </div>

      <div className="flex gap-6 flex-wrap">
        {testimonials.length ? (
          testimonials.map((testimonial) => (
            <div
              key={testimonial._id}
              className="bg-gray-100 border shadow-lg w-72 rounded-xl"
            >
              <Image
                src={testimonial.image || "/no-image.png"}
                width={240}
                height={240}
                alt="testimonial image"
                className="rounded-t-xl h-60 w-full object-cover"
              />
              <div>
                <h2 className="text-xl font-semibold pt-3 pb-1 px-2">
                  {testimonial.name}
                </h2>
                <p className="text-sm px-2 pb-1 text-gray-600">
                  <span>Working at </span>
                  {testimonial.company} <span> as </span>
                  {testimonial.designation}
                </p>
                <p className="text-sm px-2 pb-2 text-gray-600">
                  <span className="font-semibold">Review: </span>
                  {testimonial.review}
                </p>

                <div className="flex items-center justify-between px-2  pb-4">
                  <div className="flex gap-2">
                    <Button
                      onClick={() => handleEdit(testimonial._id)}
                      className="bg-gray-200 hover:bg-gray-300 cursor-pointer"
                    >
                      <FilePenLine />
                    </Button>
                    <Button
                      variant="destructive"
                      onClick={() => handleDelete(testimonial._id)}
                      className="bg-gray-200 hover:bg-gray-300 cursor-pointer"
                    >
                      <Trash2 className="text-red-500" />
                    </Button>
                  </div>
                  {testimonial.linkedinUrl && (
                    <Link
                      href={testimonial.linkedinUrl}
                      className="bg-gray-200 hover:bg-gray-300 cursor-pointer text-sm test-gray-600 py-2 px-4 rounded-lg"
                    >
                      View Profile
                    </Link>
                  )}
                </div>
              </div>
            </div>
          ))
        ) : (
          <p>No Testimonials Found....</p>
        )}
      </div>
    </div>
  );
};

export default page;
