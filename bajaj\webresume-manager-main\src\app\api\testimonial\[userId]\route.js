// app/api/testimonial/[userId]

import { auth } from "@/lib/auth";
import { connectToDB } from "@/lib/mongodb";
import Testimonial from "@/models/testimonial";
import User from "@/models/user";
import mongoose from "mongoose";
import { NextResponse } from "next/server";
import { z } from "zod";

export async function POST(req) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized", success: false });
    }

    await connectToDB();

    const user = await User.findOne({
      email: session.user.email,
    });

    if (!user) {
      return NextResponse.json({ error: "User not found", success: false });
    }

    const body = await req.json();

    const schema = z.object({
      name: z.string().min(1),
      designation: z.string().optional(),
      company: z.string().optional(),
      review: z.string().min(10),
      linkedinUrl: z.string().optional(),
      image: z.string().optional(),
    });

    const parsed = schema.safeParse(body);
    if (!parsed.success) {
      return NextResponse.json({
        error: parsed.error.flatten().fieldErrors,
        success: false,
      });
    }

    const testimonial = new Testimonial({
      name: parsed.data.name,
      designation: parsed.data.designation,
      company: parsed.data.company,
      review: parsed.data.review,
      linkedinUrl: parsed.data.linkedinUrl,
      image: parsed.data.image || "",
      user: user._id,
    });

    await testimonial.save();

    return NextResponse.json(testimonial, { status: 201 });
  } catch (error) {
    console.error("POST error:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

export async function GET(req, { params }) {
  try {
    await connectToDB();
    const { userId } = await params;

    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return NextResponse.json({ error: "Invalid user ID", success: false });
    }

    const testimonials = await Testimonial.find({ user: userId }).sort({
      createdAt: -1,
    });
    return NextResponse.json(testimonials, { status: 200 });
  } catch (error) {
    console.error("GET testimonial error:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
