import { Demo<PERSON>ontainer } from '@mui/x-date-pickers/internals/demo';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';

import {
    Autocomplete,
    Box,
    Button,
    Checkbox,
    Chip,
    CircularProgress,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    FormControl,
    FormControlLabel,
    FormGroup,
    FormHelperText,
    Grid,
    InputLabel,
    MenuItem,
    Radio,
    RadioGroup,
    Select,
    styled,
    Switch,
    TextField,
} from "@mui/material";
import { useEffect, useState } from "react";
import * as React from 'react';
import dayjs from 'dayjs';
import InputDetails from "../InputDetails";
import OutputDetails from "../OutputDetails";
import httpclient from "../../Utils";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Tooltip } from 'rsuite';
import { Visibility } from '@mui/icons-material';
import ViewMembers from '../../Pages/PushNotification/ViewMembers';


const StyledHeaderTitle = styled(DialogTitle)(({ theme }) => ({
    background: theme.palette.primary.main,
    color: "#fff",
    position: "relative",
    "& button": {
        position: "absolute",
        right: "15px",
        top: "15px",
        color: "#fff",
    },
}));

const EditPushNotification = (props) => {
    const [dialogDetails, setDialogDetails] = useState({
        open: true,
        success: false,
    });

    const [scheduledDateValue, setscheduledDateValue] = useState("");

    const [type, setType] = useState("store");

    const [formData, setFormData] = useState({

        title: props.viewDetails.title ? props.viewDetails.title : "",
        is_created_schedule: props.viewDetails.is_created_schedule ? props.viewDetails.is_created_schedule : 0,
        schedule_date: props.viewDetails.schedule_date ? props.viewDetails.schedule_date : "",
        message_send_by_type: props.viewDetails.message_send_by_type ? props.viewDetails.message_send_by_type : "store",

        message: props.viewDetails.message ? props.viewDetails.message : "",
        send_type_id: [],
    });

    const [viewDetails, setViewDetails] = useState({});
    const [pointUsedType, setPointUsedType] = useState([]);
    const [productList, setProductList] = useState([]);
    const [pointUsedTypeListLoading, setPointUsedTypeListLoading] = useState(false);
    const [openViewMembersDialog, setOpenViewMembersDialog] = useState(false);
    const [productListLoading, setProductListLoading] = useState(false);
    const [chips, setChips] = useState([]);
    const [inputValue, setInputValue] = useState([]);
    const [regulateType, setRegulateType] = useState("list");
    const [requestType, setRequestType] = useState("id");
    const [callBackUrl, setCallBackUrl] = useState("lightspeed/loyalty/store-list");
    const [validationErrors, setValidationErrors] = useState({});


    useEffect(() => {
        getPointUsedTypeList();
    }, []);

    const getPointUsedTypeList = () => {
        setPointUsedTypeListLoading(true);
        httpclient.get(`request-response?requestName=lightspeed/loyalty/send-push-notification-type-list`).then(({ data }) => {
            if (data.status === 200) {
                setPointUsedType(data.data);
                setPointUsedTypeListLoading(false);
            } else {
                setPointUsedTypeListLoading(false);
            }
        })
    };

    useEffect(() => {
        if (props.viewDetails.sendNotificationTypeList) {
            setType(props.viewDetails.message_send_by_type)
            setRegulateType((props.viewDetails.message_send_by_type === "store" || props.viewDetails.message_send_by_type === "state") ? "list" : props.viewDetails.message_send_by_type === "user_list" ? "autocomplete" : "")
            setRequestType((props.viewDetails.message_send_by_type === "store" || props.viewDetails.message_send_by_type === "user_list" ) ? "id" : props.viewDetails.message_send_by_type === "state" ? "name" : "")
            setCallBackUrl(props.viewDetails.message_send_by_type === "store" ? "lightspeed/loyalty/store-list" : props.viewDetails.message_send_by_type === "state" ? "lightspeed/loyalty/state-list" : props.viewDetails.message_send_by_type === "user_list" ? "lightspeed/lite-card-member-list" : "")
            const sendTypeIds = props.viewDetails.sendNotificationTypeList.map(item => props.viewDetails.message_send_by_type === "store" ? item.type_id : props.viewDetails.message_send_by_type === "user_list" ? item.type_id : item.value);
            setChips(props.viewDetails.sendNotificationTypeList.map(item => props.viewDetails.message_send_by_type === "postcode" ? item.type_id : []))
            setFormData({
                ...formData,
                send_type_id: sendTypeIds,
            });
        }
    }, [props.viewDetails]);


    const handleSwitch = (event) => {
        const isChecked = event.target.checked;
        setFormData({ ...formData, is_created_schedule: isChecked === true ? 1 : 0 });
    };


    const handleChangeRadioUsedTime = (event) => {
        setProductList([])
        setChips([])
        setFormData({
            ...formData,
            send_type_id: [],
            message_send_by_type: event.target.value
        });

        const selectedPoint = pointUsedType.find(point => point.code === event.target.value);

        if (selectedPoint) {
            setType(event.target.value);
            setRequestType(selectedPoint.request_name);
            setRegulateType(selectedPoint.type);
            setCallBackUrl(selectedPoint.call_back_url);
        }
    };

    useEffect(() => {
        if (callBackUrl && (regulateType === "list" || regulateType === "autocomplete")) {
            getProductList(callBackUrl);
        }
    }, [callBackUrl, regulateType]);


    const getProductList = (url) => {
        setProductListLoading(true);
        httpclient.get(`request-response?requestName=${url}`).then(({ data }) => {
            if (data.status === 200) {
                setProductList(data.data);
                setProductListLoading(false);
            } else {
                setProductListLoading(false);
            }
        })
    };


    useEffect(() => {
        if (scheduledDateValue !== "") {
            const formattedscheduledDate = dayjs(scheduledDateValue).format("YYYY-MM-DD HH:mm");
            setFormData({
                ...formData,
                schedule_date: formattedscheduledDate
            })
        }
    }, [scheduledDateValue]);


    useEffect(() => {
        props.sendEdit(dialogDetails, formData);
    }, [dialogDetails]);


    const handleClose = () => {
        setDialogDetails({
            ...dialogDetails,
            open: false,
        });
    };

    const handleYes = () => {
        let errors = {};

        if (!formData.title.trim()) {
            errors.title = "Title is required.";
        }
        if (!formData.message.trim()) {
            errors.message = "Message is required.";
        }
        if (!formData.schedule_date) {
            errors.schedule_date = "Scheduled Date is required.";
        }
        if ((type === "postcode" ? chips.length === 0 : formData.send_type_id.length === 0)) {
            errors.send_type_id = "At least one parameter is required.";
        }

        if (Object.keys(errors).length > 0) {
            setValidationErrors(errors);
            return;
        }
        else {

            setDialogDetails({
                ...dialogDetails,
                open: false,
                success: true,
            });
        }


    };

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData((prevData) => ({
            ...prevData,
            [name]: value,
        }));
        validateField(name, value);

    };

    const handleAutocompleteChange = (name) => (event, newValue) => {
        setFormData((prevData) => ({
            ...prevData,
            [name]: newValue.map((item) => (requestType === "id" ? item.lite_card_member_id : item.name)), 
        }));
        validateField(name, newValue.map((item) => (requestType === "id" ? item.lite_card_member_id : item.name)));
    };

    const handleChangeInputValue = (e) => {
        setInputValue(e.target.value);
    };

    const handleKeyDown = (event) => {
        if (event.key === "Enter" && inputValue.trim() !== "") {
            event.preventDefault();
            const newChips = inputValue.split(",").map((chip) => chip.trim());
            const uniqueChips = [...new Set([...chips, ...newChips])];

            setChips(uniqueChips);
            setFormData((prevData) => ({
                ...prevData,
                send_type_id: uniqueChips,
            }));
            setInputValue("");
            validateField("send_type_id", [...chips, ...newChips]);
        }
    };

    const handleDelete = (chipToDelete) => {
        const updatedChips = chips.filter((chip) => chip !== chipToDelete);
        setChips(updatedChips);
        setFormData((prevData) => ({
            ...prevData,
            send_type_id: updatedChips,
        }));
        validateField("send_type_id", updatedChips);
    };

    const validateField = (name, value) => {
        let error = "";

        if (name === "title" && !value.trim()) {
            error = "Title is required.";
        } else if (name === "send_type_id" && (type === "postcode" ? chips.length === 0 : value.length === 0)) {
            error = "At least one parameter is required.";
        } else if (name === "schedule_date" && value === "") {
            error = "Scheduled date is mandatory.";
        } else if (name === "message" && !value.trim()) {
            error = "Message is required.";
        }

        setValidationErrors((prevErrors) => ({
            ...prevErrors,
            [name]: error
        }));
    };


    const handleCloseViewMembers = () => {
        setOpenViewMembersDialog(false)
        setViewDetails({});


    };

    const handleViewMembers = (type, ids) => {
        setOpenViewMembersDialog(true)
        setViewDetails({ type: type, ids: ids });

    }

    const [selectedNames, setSelectedNames] = useState([]);

    useEffect(() => {
        if (formData.send_type_id?.length) {
            const selectedNamesList = productList
                ?.filter((item) => formData.send_type_id.includes(requestType === "id" ? item.id : item.name))
                .map((item) => item.name);

            setSelectedNames(selectedNamesList);
        } else {
            setSelectedNames([]);
        }
    }, [formData.send_type_id, productList, requestType]);


    return (
        <div>
            <Dialog
                open={dialogDetails.open}
                onClose={handleClose}
                maxWidth="lg"
                fullWidth
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <StyledHeaderTitle id="alert-dialog-title">
                    {props.viewDetails.id ? `Edit Notification` : `Add Notification`}
                </StyledHeaderTitle>
                <DialogContent>
                    <Box pt={3}>
                        <Box p={3} sx={{
                            boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)"
                        }}>
                            <h5 style={{ margin: 0 }}><i>{`Fields with * are mandatory`}</i></h5>
                            <Grid container spacing={2}>
                                <Grid item xs={12} md={9}>
                                    <TextField
                                        required
                                        label="Title"
                                        name="title"
                                        value={formData.title}
                                        onChange={handleChange}
                                        fullWidth
                                        margin="normal"
                                        error={!!validationErrors.title}
                                        helperText={validationErrors.title}
                                    />

                                </Grid>
                                <Grid item xs={12} md={3}>
                                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: "flex-end" }}>
                                        <span>Push To LiteCard?</span>
                                        <Switch
                                            checked={formData.is_created_schedule === 1 ? true : false}
                                            onChange={handleSwitch}
                                            inputProps={{ 'aria-label': 'controlled' }}
                                        />
                                    </div>

                                </Grid>
                                <Grid item xs={12} md={6}>
                                    <FormControl required fullWidth error={!!validationErrors.schedule_date}>
                                        <LocalizationProvider dateAdapter={AdapterDayjs}>
                                            <DemoContainer components={['DateTimePicker']}>
                                                <DateTimePicker
                                                    label="Scheduled Date"
                                                    name="schedule_date"
                                                    value={formData.schedule_date ? dayjs(formData.schedule_date) : null}
                                                    onChange={(newValue) => setscheduledDateValue(newValue)}
                                                    slotProps={{
                                                        textField: {
                                                            error: !!validationErrors.schedule_date,
                                                        }
                                                    }}
                                                />
                                            </DemoContainer>
                                        </LocalizationProvider>
                                        {!!validationErrors.schedule_date && (
                                            <FormHelperText>{validationErrors.schedule_date}</FormHelperText>
                                        )}
                                    </FormControl>

                                </Grid>

                            </Grid>

                            <Grid container spacing={2}>
                                <Grid item xs={12} md={6}>
                                    <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginTop: "20px" }}>
                                        <h4 style={{ margin: 0, fontWeight: "inherit" }}>Message Send By Type</h4>
                                        <FormControl>
                                            <RadioGroup
                                                row
                                                aria-labelledby="demo-controlled-radio-buttons-group"
                                                name="controlled-radio-buttons-group"
                                                value={formData.message_send_by_type}
                                                onChange={handleChangeRadioUsedTime}
                                            >
                                                {pointUsedType && pointUsedType.map((point) => (
                                                    <FormControlLabel value={point.code} control={<Radio />} label={point.name} />

                                                ))}
                                            </RadioGroup>
                                        </FormControl>

                                    </div>

                                </Grid>
                                <Grid item xs={12} md={5}>
                                    {regulateType == "list" ?
                                        <FormControl required fullWidth error={!!validationErrors.send_type_id}>
                                            <InputLabel>{productListLoading ? "Please Wait.." : `Select ${type}`}</InputLabel>
                                            <Select
                                                multiple
                                                value={formData.send_type_id || []}
                                                label={productListLoading ? "Please Wait.." : `Select ${type}`}
                                                name="send_type_id"
                                                onChange={handleChange}
                                                renderValue={(selected) =>
                                                    productList
                                                        ?.filter((item) => selected.includes(requestType === "id" ? item.id : item.name))
                                                        .map((item) => item.name)
                                                        .join(", ") || `Select ${type}`
                                                }
                                            >
                                                <MenuItem value="">
                                                    <em>Select {type}</em>
                                                </MenuItem>
                                                {productList &&
                                                    productList.map((point) => (
                                                        <MenuItem key={point.id} value={requestType === "id" ? point.id : point.name}>
                                                            <Checkbox checked={formData.send_type_id?.includes(requestType === "id" ? point.id : point.name)} />
                                                            {point.name}
                                                        </MenuItem>
                                                    ))}
                                            </Select>

                                            {productListLoading && (
                                                <CircularProgress
                                                    size={24}
                                                    style={{
                                                        position: "absolute",
                                                        top: "50%",
                                                        right: 16,
                                                        marginTop: -12,
                                                        marginRight: 10,
                                                    }}
                                                />
                                            )}
                                            {!!validationErrors.send_type_id && (
                                                <FormHelperText>{validationErrors.send_type_id}</FormHelperText>
                                            )}
                                        </FormControl>
                                        : regulateType === "autocomplete" ?
                                            <FormControl required fullWidth error={!!validationErrors.send_type_id}>
                                                <Autocomplete
                                                    multiple
                                                    options={productList || []}
                                                    getOptionLabel={(option) => (requestType === "id" ? option.name : option.name)}
                                                    isOptionEqualToValue={(option, value) => option.lite_card_member_id
                                                        == value.lite_card_member_id
                                                    }
                                                    value={productList?.filter((item) =>
                                                        (formData.send_type_id || []).includes(requestType === "id" ? item.lite_card_member_id
                                                            : item.name)
                                                    )}
                                                    // onChange={(event, newValue) => {
                                                    //     setFormData({
                                                    //         ...formData,
                                                    //         send_type_id: newValue.map((item) => (requestType === "id" ? item.id : item.name)),
                                                    //     });
                                                    // }}
                                                    onChange={handleAutocompleteChange("send_type_id")}
                                                    loading={productListLoading}
                                                    renderInput={(params) => (
                                                        <TextField
                                                            {...params}
                                                            label={productListLoading ? "Please Wait..." : `Select ${type}`}
                                                            variant="outlined"
                                                            InputProps={{
                                                                ...params.InputProps,
                                                                endAdornment: (
                                                                    <>
                                                                        {productListLoading ? <CircularProgress color="inherit" size={20} /> : null}
                                                                        {params.InputProps.endAdornment}
                                                                    </>
                                                                ),
                                                            }}
                                                        />
                                                    )}
                                                />
                                                {!!validationErrors.send_type_id && (
                                                    <FormHelperText>{validationErrors.send_type_id}</FormHelperText>
                                                )}
                                            </FormControl>
                                            :
                                            <FormControl required fullWidth>
                                                <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                                                    {chips.map((chip, index) => (
                                                        <Chip key={index} label={chip} onDelete={() => handleDelete(chip)} />
                                                    ))}
                                                </Box>

                                                <TextField
                                                    label="Type Postcode here"
                                                    name="send_type_id"
                                                    value={inputValue}
                                                    onChange={handleChangeInputValue}
                                                    onKeyDown={handleKeyDown}
                                                    fullWidth
                                                    margin="normal"
                                                    error={!!validationErrors.send_type_id}
                                                    helperText={validationErrors.send_type_id ? validationErrors.send_type_id : "Press Enter to save individual postcode."}
                                                />
                                            </FormControl>
                                    }


                                </Grid>
                                <Grid item xs={12} md={1}>
                                    <Tooltip title="View Members">
                                        <IconButton onClick={() => handleViewMembers(type, formData.send_type_id)} disabled={formData.send_type_id.length === 0} style={{ padding: "10px", borderRadius: "50%" }}>
                                            <Visibility fontSize="small" color="primary" />
                                        </IconButton>
                                    </Tooltip>
                                </Grid>
                            </Grid>


                            <FormControl fullWidth>
                                <TextField
                                    required
                                    label="Type message here"
                                    name="message"
                                    value={formData.message}
                                    onChange={handleChange}
                                    fullWidth
                                    margin="normal"
                                    multiline
                                    rows={3}
                                    error={!!validationErrors.message}
                                    helperText={validationErrors.message}
                                />
                            </FormControl>
                        </Box>


                    </Box>
                </DialogContent>
                <DialogActions styled={{ margin: "5px 10px" }}>
                    <Button onClick={handleClose} color="error" variant="contained" autoFocus>
                        {"Cancel"}
                    </Button>

                    <Button
                        onClick={handleYes}
                        color="primary"
                        variant="contained"
                        autoFocus
                    >
                        {props.viewDetails.id ? `Edit` : `Save`}
                    </Button>

                </DialogActions>
            </Dialog>
            {openViewMembersDialog && (
                <ViewMembers
                    viewDetails={viewDetails}
                    handleCloseViewMembers={handleCloseViewMembers}
                    regulateType={regulateType}
                    selectedNames={selectedNames}
                    chips={chips}
                />
            )}
        </div>
    );
};

export default EditPushNotification;
