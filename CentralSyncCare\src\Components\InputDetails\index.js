import { Close } from "@mui/icons-material";
import { Autocomplete, Button, Checkbox, CircularProgress, FormControl, FormControlLabel, FormHelperText, Grid, IconButton, InputLabel, MenuItem, Radio, RadioGroup, Select, TextField, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";

const InputDetails = (props) => {


    const [categoryValue, setCategoryValue] = useState([]);
    const [productValue, setProductValue] = useState([]);
    const [isCategoryDisabled, setIsCategoryDisabled] = useState(false);
    const [isProductDisabled, setIsProductDisabled] = useState(false);


    useEffect(() => {
        if (props.inputDetails?.length > 0) {
            const categories = props.inputDetails.map(detail => typeof detail.value === "string" ? detail.value : detail.value?.length > 0 && detail.value?.map((m) => m.categoryValue || []).flat() || []).flat();
            const products = props.inputDetails.map(detail => typeof detail.value === "string" ? detail.value : detail.value?.length > 0 && detail.value?.map((m) => m.productValue || []).flat() || []).flat();

            setCategoryValue(categories);
            setProductValue(products);
        }
    }, [props.inputDetails]);

    const handleCategoryChange = (index, newCategoryValue, code) => {
        props.setCode(code);
        const categoryIds = newCategoryValue.map((item) => item.id);
        setCategoryValue(categoryIds);
        if (categoryIds.length > 0) {
            setProductValue([]);
            setIsProductDisabled(true);
        } else {
            setIsProductDisabled(false);
        }
        props.getProductListFilter(categoryIds)
        props.updateInputDetail(index, "value", [{
            categoryValue: categoryIds,
        }]);
    };

    const handleProductChange = (index, newProductValue, code) => {
        props.setCode(code);
        const productIds = newProductValue.map((item) => item.id);
        setProductValue(productIds);
        if (productIds.length > 0) {
            setCategoryValue([]);
            setIsCategoryDisabled(true);
        } else {
            setIsCategoryDisabled(false);
        }
        props.updateInputDetail(index, "value", [{
            categoryValue,
            productValue: productIds,
        }]);
    };


    return (
        <div>
            <h4>Input Details</h4>
            {props.inputDetails.map((detail, index) => (
                <Grid container spacing={2}>

                    <Grid item xs={12} md={4}>
                        <>
                            <div key={index} style={{ marginBottom: "20px" }}>
                                {index > 0 && (
                                    <div style={{ marginBottom: "10px" }}>
                                        <h4 style={{ margin: 0, fontWeight: "inherit" }}>Condition</h4>

                                        <FormControl>
                                            <RadioGroup
                                                row
                                                aria-labelledby={`condition-radio-buttons-${index}`}
                                                name={`condition-${index}`}
                                                value={detail.condition}
                                                onChange={(e) =>
                                                    props.updateInputDetail(index, "condition", e.target.value)
                                                }
                                            >
                                                <FormControlLabel value="and" control={<Radio />} label="AND" />
                                                <FormControlLabel value="or" control={<Radio />} label="OR" />
                                            </RadioGroup>
                                        </FormControl>

                                    </div>



                                )}


                                <div>
                                    <FormControl fullWidth error={!!props.validationErrors}>
                                        <InputLabel>{props.inputListLoading ? "Please Wait.." : "Input Type"}</InputLabel>
                                        <Select
                                            value={detail.input_output_id}
                                            label={props.inputListLoading ? "Please Wait.." : "Input Type"}
                                            onChange={(e) =>
                                                props.updateInputDetail(index, "input_output_id", e.target.value)
                                            }
                                            renderValue={(selected) => {
                                                const selectedItem = props.inputData.find(item => item.id === selected);
                                                return selectedItem ? selectedItem.name : selected;
                                            }}

                                        >
                                            <MenuItem value="" disabled={index === 0}>Select Input</MenuItem>
                                            {!props.view && index !== 0 && props.inputData.map((item) => (
                                                <MenuItem
                                                    key={item.id}
                                                    value={item.id}
                                                    disabled={props.inputDetails.some(
                                                        (input, idx) => input.input_output_id === item.id && idx !== index
                                                    )}>
                                                    {item.name}
                                                </MenuItem>
                                            ))}


                                        </Select>
                                        {props.inputListLoading && (
                                            <CircularProgress
                                                size={24}
                                                style={{
                                                    position: "absolute",
                                                    top: "50%",
                                                    right: 16,
                                                    marginTop: -12,
                                                    marginRight: 10,
                                                }}
                                            />
                                        )}
                                        {!!props.validationErrors && (
                                            <FormHelperText>{props.validationErrors}</FormHelperText>
                                        )}
                                    </FormControl>
                                </div>


                            </div>
                        </>
                    </Grid>

                    <Grid item xs={12} md={8}>

                        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                            {detail.input_output_id && (
                                <div style={{ marginTop: index > 0 ? "75px" : "0px" }}>
                                    {(() => {
                                        const selectedInput = props.inputData.find(
                                            (item) => item.id === parseInt(detail.input_output_id)
                                        );

                                        if (selectedInput?.input_type === "text" && selectedInput.is_value_required) {
                                            return (
                                                <div>
                                                    <TextField
                                                        fullWidth
                                                        sx={{ minWidth: "400px" }}
                                                        required
                                                        label="Input Value"
                                                        placeholder={selectedInput.symbol || ""}
                                                        value={detail.value}
                                                        onChange={(e) =>
                                                            props.updateInputDetail(index, "value", e.target.value)
                                                        }
                                                    />
                                                </div>
                                            );
                                        }

                                        if (selectedInput?.input_type === "list" && (selectedInput?.input_output_code === "CLI") && selectedInput.is_value_required) {
                                            return (
                                                <Grid container spacing={2}>
                                                    {/* Category Selection */}

                                                    <Grid item xs={7}>
                                                        <FormControl fullWidth>
                                                            <Autocomplete
                                                                multiple
                                                                options={props.productCategoryList || []}
                                                                getOptionLabel={(option) => option.name || ""}
                                                                isOptionEqualToValue={(option, value) => option.id === value.id}
                                                                value={props.productCategoryList.filter(cat => categoryValue.includes(cat.id))}
                                                                onChange={(event, newValue) => handleCategoryChange(index, newValue, "CLI")}
                                                                loading={props.productCategoryLoading}
                                                                disabled={isCategoryDisabled}
                                                                renderInput={(params) => (
                                                                    <TextField
                                                                        {...params}
                                                                        label={props.productCategoryLoading ? "Please Wait..." : "Select Category"}
                                                                        sx={{ minWidth: "300px" }}
                                                                        InputProps={{
                                                                            ...params.InputProps,
                                                                            endAdornment: (
                                                                                <>
                                                                                    {props.productCategoryLoading ? <CircularProgress color="inherit" size={20} /> : null}
                                                                                    {params.InputProps.endAdornment}
                                                                                </>
                                                                            ),
                                                                        }}
                                                                    />
                                                                )}
                                                            />
                                                        </FormControl>

                                                        <Typography variant="body2" color="error" sx={{ mt: 0.6 }}>
                                                            *Please select either Category or Product to continue*
                                                        </Typography>

                                                    </Grid>

                                                    {/* Product Selection */}
                                                    <Grid item xs={5}>
                                                        <FormControl fullWidth>
                                                            <Autocomplete
                                                                multiple
                                                                options={props.productList || []}
                                                                getOptionLabel={(option) => option.name || ""}
                                                                isOptionEqualToValue={(option, value) => option.id === value.id}
                                                                value={props.productList.filter(prod => productValue.includes(prod.id))}
                                                                onChange={(event, newValue) => handleProductChange(index, newValue, "CLI")}
                                                                loading={props.productListLoading}
                                                                disabled={isProductDisabled}
                                                                renderInput={(params) => (
                                                                    <TextField
                                                                        {...params}
                                                                        label={props.productListLoading ? "Please Wait..." : "Select Products"}
                                                                        sx={{ minWidth: "300px" }}
                                                                        InputProps={{
                                                                            ...params.InputProps,
                                                                            endAdornment: (
                                                                                <>
                                                                                    {props.productListLoading ? <CircularProgress color="inherit" size={20} /> : null}
                                                                                    {params.InputProps.endAdornment}
                                                                                </>
                                                                            ),
                                                                        }}
                                                                    />
                                                                )}
                                                            />
                                                        </FormControl>
                                                    </Grid>
                                                </Grid>
                                            );
                                        }

                                        if (selectedInput?.input_type === "list" && selectedInput.is_value_required) {
                                            return (
                                                <div>
                                                    {/* <FormControl fullWidth>
                                                        <InputLabel>{props.plateformListLoading ? "Please Wait.." : "Select Value"}</InputLabel>
                                                        <Select
                                                            label={props.plateformListLoading ? "Please Wait.." : "Select Value"}
                                                            onChange={(e) =>
                                                                props.updateInputDetail(index, "value", e.target.value)
                                                            }
                                                            value={detail.value}
                                                            sx={{ minWidth: "400px" }}
                                                        >
                                                            <MenuItem value="">Select from list</MenuItem>
                                                 
                                                            {props.platformList.map((item) => (
                                                                <MenuItem value={String(item.id)}>
                                                                    {item.name}
                                                                </MenuItem>
                                                            ))}
                                                        </Select>
                                                        {props.plateformListLoading && (
                                                            <CircularProgress
                                                                size={24}
                                                                style={{
                                                                    position: "absolute",
                                                                    top: "50%",
                                                                    right: 16,
                                                                    marginTop: -12,
                                                                    marginRight: 10,
                                                                }}
                                                            />
                                                        )}
                                                    </FormControl> */}

                                                    <FormControl fullWidth>
                                                        <InputLabel>{props.plateformListLoading ? "Please Wait.." : "Select Value"}</InputLabel>
                                                        <Select
                                                            multiple
                                                            label={props.plateformListLoading ? "Please Wait.." : "Select Value"}
                                                            value={detail.value || []}
                                                            onChange={(e) => {
                                                                const selectedValues = e.target.value;
                                                                props.updateInputDetail(index, "value", selectedValues);
                                                            }}
                                                            sx={{ minWidth: "400px" }}
                                                            renderValue={(selected) =>
                                                                props.platformList
                                                                    .filter((item) => selected.includes(String(item.id)))
                                                                    .map((item) => item.name)
                                                                    .join(", ")
                                                            }
                                                        >
                                                            {!props.view && props.platformList.map((item) => (
                                                                <MenuItem key={item.id} value={String(item.id)}>
                                                                    <Checkbox checked={detail.value?.includes(String(item.id)) || false} />
                                                                    {item.name}
                                                                </MenuItem>
                                                            ))}
                                                        </Select>
                                                        {props.plateformListLoading && (
                                                            <CircularProgress
                                                                size={24}
                                                                style={{
                                                                    position: "absolute",
                                                                    top: "50%",
                                                                    right: 16,
                                                                    marginTop: -12,
                                                                    marginRight: 10,
                                                                }}
                                                            />
                                                        )}
                                                    </FormControl>
                                                </div>
                                            );
                                        }
                                    })()}
                                </div>
                            )}

                            {!props.view && props.inputDetails.length > 1 && index !== 0 && (
                                <IconButton
                                    style={{ marginLeft: "20px" }}
                                    onClick={() => props.removeInputDetail(index)}
                                >
                                    <Close color={"error"} />
                                </IconButton>
                            )}

                        </div>
                    </Grid>
                </Grid>
            ))}

            {!props.view && <Button variant={"contained"} onClick={props.addInputDetail} disabled={props.isAddButtonDisabled}>+ Add Input</Button>}
        </div>

    );
};

export default InputDetails;
