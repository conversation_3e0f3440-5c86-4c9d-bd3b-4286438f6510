import React, { useEffect, useState } from "react";
import { Box, Button, Select, MenuItem, Typography, Input, List, ListItem, FormControl, InputLabel, Snackbar, CircularProgress } from "@mui/material";
import httpclient from "../../../Utils";
import useTokenRefresh from "../../../Hooks/useTokenRefresh";
import MuiAlert from "@mui/material/Alert";
import { Info } from "@mui/icons-material";

import {
  Dialog,
  DialogTitle,
  DialogContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
} from "@mui/material";
const Alert = React.forwardRef(function Alert(props, ref) {
  return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

const ImportCountriesPostCode = () => {

  const { getTokenRefreshed: refresh, open: tokenOpen, overlay: overlay, setOpen: setTokenOpen, message: tokenMessage, messageState: tokenMessageState } = useTokenRefresh();

  const [selectedFiles, setSelectedFiles] = useState([]);
  const [countries, setCountries] = useState([]);
  const [uploadFile, setUploadFile] = useState(null);
  const [selectedFilesPostcodes, setSelectedFilesPostcodes] = useState([]);
  const [uploadPostcodesFile, setUploadPostcodesFile] = useState(null);
  const [countryID, setCountryID] = useState("");
  const [open, setOpen] = useState(false);
  const [message, setMessage] = useState("");
  const [messageState, setMessageState] = useState("");
  const [infoOpen, setInfoOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loadingExport, setLoadingExport] = useState(false);
  const [loadingPostExport, setLoadingPostExport] = useState(false);

  useEffect(() => {
    getAllCountries();
  }, []);

  const getAllCountries = () => {
    httpclient.get(`request-response?requestName=get-options&type=Country&isDataAll=1`).then(({ data }) => {
      if (data.status === 200) {
        setCountries(data.data);

      }
    }
    )
  }

  const handleChange = (e) => {
    setCountryID(e.target.value);
  };

  const handleFileChange = (event, setFiles) => {
    const files = Array.from(event.target.files);
    const csvFiles = files.filter((file) => file.type === "text/csv");

    if (csvFiles.length === 0) {
      alert("Please upload valid CSV files.");
    } else {
      setFiles((prevFiles) => [...prevFiles, ...csvFiles]);
      setUploadFile(csvFiles[csvFiles.length - 1]);
    }
  };

  useEffect(() => {
    if (uploadFile !== null) {
      uploadCountryFiles(uploadFile, "country");
    }
  }, [uploadFile]);

  useEffect(() => {
    if (uploadPostcodesFile !== null) {
      uploadCountryFiles(uploadPostcodesFile, "postcode");
    }
  }, [uploadPostcodesFile]);

  const uploadCountryFiles = (file, type) => {
    const formData = new FormData();
    formData.append("file", file); // Attach the file
    formData.append("type", type);
    formData.append("isFileUpload", 1);
    if (type === "postcode") {
      formData.append("countryID", countryID);
    }

    httpclient
      .post(`request-response?requestName=upload-options`, formData, {
        headers: {
          "Content-Type": "multipart/form-data", // Ensure correct content type
        },
      })
      .then(({ data }) => {
        if (data.status === 200) {
          if (type === "country") {
            setUploadFile(null);
          } else {
            setUploadPostcodesFile(null);
          }
          setOpen(true);
          setMessage(data.message);
          setMessageState("success");
          setLoading(false);

        } else {

          setOpen(true);
          setMessage(data.message);
          setMessageState("error");
          setLoading(false);
        }
      }
      ).catch((err) => {
        if (err.response.status === 401) {
          if (type === "country") {
            setSelectedFiles((prevFiles) => prevFiles.slice(0, -1));
          } else {
            setSelectedFilesPostcodes((prevFiles) => prevFiles.slice(0, -1));
          }
          refresh();
          setOpen(tokenOpen);
          setMessage(tokenMessage);
          setMessageState("error");
        } else if (err.response.status === 422) {
          if (type === "country") {
            setSelectedFiles((prevFiles) => prevFiles.slice(0, -1));
          } else {
            setSelectedFilesPostcodes((prevFiles) => prevFiles.slice(0, -1));
          }
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setLoading(false);
        } else if (err.response.status === 400) {
          if (type === "country") {
            setSelectedFiles((prevFiles) => prevFiles.slice(0, -1));
          } else {
            setSelectedFilesPostcodes((prevFiles) => prevFiles.slice(0, -1));
          }
          //const errorMessages = Object.values(err.response.data.errors).flat();
          const errorMessages = err.response.data.message;
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setLoading(false);

        } else {
          if (type === "country") {
            setSelectedFiles((prevFiles) => prevFiles.slice(0, -1));
          } else {
            setSelectedFilesPostcodes((prevFiles) => prevFiles.slice(0, -1));
          }
          setOpen(true);
          setMessage(err.response.data.message);
          setMessageState("error");
          setLoading(false);
        }
      })
  }

  const handlePostcodesFileChange = (event, setFiles) => {
    const files = Array.from(event.target.files);
    const csvFiles = files.filter((file) => file.type === "text/csv");

    if (csvFiles.length === 0) {
      alert("Please upload valid CSV files.");
    } else {
      setFiles((prevFiles) => [...prevFiles, ...csvFiles]);
      setUploadPostcodesFile(csvFiles[csvFiles.length - 1]);
    }
  };

  const handleExport = (type) => {
    if (type === "postcode") {
      setLoadingPostExport(true);
    } else {
      setLoadingExport(true);
    }
    httpclient
      .get(
        `request-response?requestName=get-options&type=${type}&isExportData=1${type === "postcode" ? `&countryID=${countryID}` : ''}`
      )

      .then((response) => {
        if (response) {
          // Generate filename with timestamp
          const getFormattedDateTime = () => {
            const now = new Date();
            const day = String(now.getDate()).padStart(2, '0');
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const year = now.getFullYear();
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            return `${day}${month}${year}${hours}${minutes}${seconds}`;
          };

          const formattedDateTime = getFormattedDateTime();
          const fileName = `${type}_export_${formattedDateTime}.csv`;

          // Create Blob URL
          const blob = new Blob([response.data], { type: "text/csv;charset=utf-8;" });
          const url = URL.createObjectURL(blob);
          const link = document.createElement("a");
          link.href = url;
          link.setAttribute("download", fileName);
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          if (type === "postcode") {
            setLoadingPostExport(false);
          } else {
            setLoadingExport(false);
          }
        }
      })
      .catch((err) => {
        if (err.response.status === 401) {
          refresh();
          setOpen(tokenOpen);
          setMessage(tokenMessage);
          setMessageState("error");
        } else if (err.response.status === 422) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          if (type === "postcode") {
            setLoadingPostExport(false);
          } else {
            setLoadingExport(false);
          }
        } else if (err.response.status === 400) {
          //const errorMessages = Object.values(err.response.data.errors).flat();
          const errorMessages = err.response.data.message;
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          if (type === "postcode") {
            setLoadingPostExport(false);
          } else {
            setLoadingExport(false);
          }

        } else {
          setOpen(true);
          setMessage(err.response.data.message);
          setMessageState("error");
          if (type === "postcode") {
            setLoadingPostExport(false);
          } else {
            setLoadingExport(false);
          }
        }
      })
  };

  const handleClose = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }
    setOpen(false);
    setTokenOpen(false);
  };


  return (
    <div>
      <Box display="flex" justifyContent="center" gap={4} p={4} width="100%">
        <Box
          sx={{
            flex: 1,
            p: 3,
            boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
            borderRadius: "8px",
          }}
        >
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div>
              <Typography fontWeight="bold" fontSize={18}>
                Import Countries
              </Typography>
              <Button variant="contained" component="label" sx={{ mt: 2 }}>
                Upload CSV File
                <input
                  type="file"
                  accept=".csv"
                  multiple
                  hidden
                  onChange={(e) => handleFileChange(e, setSelectedFiles)}
                />
              </Button>
              <IconButton color="primary" sx={{ mt: 2 }} onClick={() => setInfoOpen(true)}>
                <Info />
              </IconButton>
            </div>
            <div style={{ marginTop: "20px" }}>
              {selectedFiles.length > 0 && (
                <>
                  <Typography fontWeight="bold" fontSize={14}>
                    Recently Uploaded:
                  </Typography>
                  <List sx={{ mt: 1, fontSize: 14, color: "green" }}>
                    {selectedFiles.map((file, index) => (
                      <ListItem key={index}>{file.name}</ListItem>
                    ))}
                  </List>
                </>
              )}
            </div>
          </div>

          <Box mt={20} display={"flex"} alignItems={"center"} gap="10px" borderTop={"1px solid grey"} paddingTop="10px">
            <Typography fontWeight={"600"}>Export Existing Countries :</Typography>
            <div>
              <Button variant="contained" onClick={() => handleExport("country")} disabled={loadingExport}>
                {loadingExport ? <CircularProgress size={24} color="inherit" /> : "Download"}
              </Button>
              <Typography fontSize={"12px"} marginTop={"8px"} marginLeft={"10px"}>CSV Format*</Typography>
            </div>
          </Box>
        </Box>

        <Box
          sx={{
            flex: 1,
            p: 3,
            boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
            borderRadius: "8px",
          }}
        >
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div>
              <Typography fontWeight="bold" fontSize={18}>
                Import Post Codes
              </Typography>
              <Box display="flex" alignItems="center" mt={2}>
                <FormControl required margin="normal">
                  <InputLabel>Select Country</InputLabel>
                  <Select
                    label="Select Country"
                    name="countryID"
                    value={countryID}
                    onChange={handleChange}
                    sx={{ minWidth: "300px" }}
                  >
                    <MenuItem value="">Select Country</MenuItem>
                    {countries && countries.map((country) => (
                      <MenuItem value={country.id}>{country.name}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
              <Button variant="contained" disabled={countryID === ""} component="label" sx={{ mt: 2 }}>
                Upload CSV File
                <input
                  type="file"
                  accept=".csv"
                  hidden
                  multiple
                  onChange={(e) => handlePostcodesFileChange(e, setSelectedFilesPostcodes)}
                />
              </Button>
              <IconButton color="primary" sx={{ mt: 2 }} onClick={() => setInfoOpen(true)}>
                <Info />
              </IconButton>
            </div>
            <div style={{ marginTop: "20px" }}>
              {selectedFilesPostcodes.length > 0 && (
                <>
                  <Typography fontWeight="bold" fontSize={14}>
                    Recently Uploaded:
                  </Typography>
                  <List sx={{ mt: 1, fontSize: 14, color: "green" }}>
                    {selectedFilesPostcodes.map((file, index) => (
                      <ListItem key={index}>{file.name}</ListItem>
                    ))}
                  </List>
                </>
              )}
            </div>
          </div>
          <Box mt={12} display={"flex"} alignItems={"center"} gap="10px" borderTop={"1px solid grey"} paddingTop="10px">
            <Typography fontWeight={"600"}>Export Existing Postcodes :</Typography>
            <div>
              <Button variant="contained" disabled={countryID === "" || loadingPostExport} onClick={() => handleExport("postcode")}>
                {loadingPostExport ? <CircularProgress size={24} color="inherit" /> : "Download"}
              </Button>
              <Typography fontSize={"12px"} marginTop={"8px"} marginLeft={"10px"}>CSV Format*</Typography>
            </div>
          </Box>
        </Box>
      </Box>
      {/* Dialog for Excel Structure */}
      <Dialog
        maxWidth="md"
        fullWidth
        open={infoOpen}
        onClose={() => setInfoOpen(false)}>
        <DialogTitle>Required CSV File Structure</DialogTitle>
        <DialogContent>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell><strong>ID</strong></TableCell>
                  <TableCell><strong>Name</strong></TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                <TableRow>
                  <TableCell>1</TableCell>
                  <TableCell>John Doe</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>2</TableCell>
                  <TableCell>Jane Smith</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>3</TableCell>
                  <TableCell>Michael Brown</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>
        </DialogContent>
      </Dialog>

      <Snackbar
        autoHideDuration={3000}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
        open={open || tokenOpen}
        onClose={handleClose}
      >
        <Alert
          onClose={handleClose}
          severity={messageState || tokenMessageState}
          sx={{ width: "100%" }}
        >
          {message || tokenMessage}
        </Alert>
      </Snackbar>
    </div>
  );
};

export default ImportCountriesPostCode;