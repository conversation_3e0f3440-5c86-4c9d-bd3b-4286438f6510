/* 360° Bike Viewer Styles */

.viewer-360-container {
  position: relative;
  width: 100%;
  height: 500px;
  border-radius: 16px;
  overflow: hidden;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
  cursor: grab;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.viewer-360-container:active {
  cursor: grabbing;
}

.viewer-360-container canvas {
  display: block;
  width: 100% !important;
  height: 100% !important;
  border-radius: 12px;
}

/* Loading Indicator */
.viewer-360-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  z-index: 10;
  transition: opacity 0.3s ease;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
}

.loading-progress {
  width: 200px;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* Error State */
.viewer-360-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  z-index: 10;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-message {
  font-size: 16px;
  font-weight: 600;
  color: #dc2626;
  margin-bottom: 16px;
  text-align: center;
}

.error-retry {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.error-retry:hover {
  background: #2563eb;
}

/* Controls */
.viewer-360-controls {
  position: absolute;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 8px 12px;
  border-radius: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 5;
}

.viewer-360-control-btn {
  background: none;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
}

.viewer-360-control-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.viewer-360-control-btn.active {
  background: #3b82f6;
  color: white;
}

/* Interaction Hints */
.viewer-360-hint {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  opacity: 0;
  animation: fadeInOut 4s ease-in-out;
  z-index: 5;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0; }
  20%, 80% { opacity: 1; }
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .viewer-360-container {
    height: 300px;
    border-radius: 8px;
  }

  .viewer-360-container canvas {
    border-radius: 8px;
  }

  .loading-text {
    font-size: 14px;
  }

  .loading-progress {
    width: 150px;
  }

  .viewer-360-controls {
    bottom: 12px;
    padding: 6px 10px;
    border-radius: 16px;
  }

  .viewer-360-control-btn {
    width: 28px;
    height: 28px;
  }

  .viewer-360-hint {
    top: 12px;
    right: 12px;
    font-size: 11px;
    padding: 6px 10px;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .viewer-360-container {
    cursor: default;
  }

  .viewer-360-container:active {
    cursor: default;
  }

  .viewer-360-control-btn:hover {
    background: none;
    color: #6b7280;
  }

  .viewer-360-control-btn:active {
    background: #f3f4f6;
    color: #374151;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .viewer-360-container {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  }
}

/* Reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  .loading-spinner {
    animation: none;
  }

  .viewer-360-hint {
    animation: none;
    opacity: 1;
  }

  .progress-bar {
    transition: none;
  }
}

/* Focus styles for accessibility */
.viewer-360-control-btn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.error-retry:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* 360° Section Styling - Mountain Background Design */
.viewer-360-section {
  min-height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Platform-style 360° Viewer Container */
.viewer-360-container-platform {
  position: relative;
  width: 700px;
  height: 500px;
  max-width: 90vw;
  margin: 0 auto;
  cursor: grab;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.viewer-360-container-platform:active {
  cursor: grabbing;
}

.viewer-360-container-platform canvas {
  display: block;
  width: 100% !important;
  height: 100% !important;
  object-fit: contain;
}



/* New Controls Styling */
.viewer-360-controls-new {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  gap: 8px;
  z-index: 10;
}

.viewer-360-control-btn-new {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: none;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #374151;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.viewer-360-control-btn-new:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.viewer-360-control-btn-new.active {
  background: #3b82f6;
  color: white;
}

.viewer-360-control-btn-new:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .viewer-360-container {
    display: none;
  }
}
