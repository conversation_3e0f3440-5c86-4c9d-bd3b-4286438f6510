"use server";

import { connectToDB } from "@/lib/mongodb";
import Testimonial from "@/models/testimonial";
import { link } from "fs";

export const deleteTestimonial = async (testimonialId) => {
  await connectToDB();

  try {
    await Testimonial.findByIdAndDelete(testimonialId);
    return;
  } catch (error) {
    console.log("Error in deleteblog action", error);
  }
};

export const getTestimonialById = async (testimonialId) => {
  await connectToDB();

  try {
    //finding existing testimonial
    const testimonial = await Testimonial.findById(testimonialId);
    const data = {
      name: testimonial.name,
      designation: testimonial.designation,
      company: testimonial.company,
      image: testimonial.image,
      review: testimonial.review,
      linkedinUrl: testimonial.linkedinUrl,
    };

    return data;
  } catch (error) {
    console.log("Error in get testimonial by id", error);
  }
};
