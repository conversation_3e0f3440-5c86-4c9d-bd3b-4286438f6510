import * as React from "react";
import { useEffect, useState } from "react";
import { styled } from "@mui/material/styles";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell, { tableCellClasses } from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import {
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Skeleton,
  Switch,
  TableFooter,
  TablePagination,
  Tooltip,
  useTheme,
} from "@mui/material";
import moment from "moment/moment";
import {
  Menu,
  Check,
  Clear,
  KeyboardArrowDown,
  KeyboardArrowLeft,
  KeyboardArrowRight,
  KeyboardArrowUp,
  UnfoldMore,
  DoubleArrow,
  Preview,
} from "@mui/icons-material";
import PropTypes from "prop-types";
import OptionMenu from "./OptionMenu";

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.light,
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
  },
  "& svg": {
    position: "relative",
    top: "5px",
  },
  "&:last-child": {
    // paddingRight: 64,
    "& svg": {
      // display: 'none',
      // color: theme.palette.primary.dark
    },
  },
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  "&:nth-of-type(odd)": {
    backgroundColor: theme.palette.action.hover,
  },
  // hide last border
  "&:last-child td, &:last-child th": {
    border: 0,
  },
}));

const StyledHeaderTitle = styled(DialogTitle)(({ theme }) => ({
  background: theme.palette.primary.main,
  color: "#fff",
  position: "relative",
  "& button": {
    position: "absolute",
    right: "15px",
    top: "15px",
    color: "#fff",
  },
}));

const SwitchDialog = (props) => {
  const [dialogDetails, setDialogDetails] = useState({
    open: true,
    success: false,
  });

  const handleClose = () => {
    setDialogDetails({
      ...dialogDetails,
      open: false,
    });
    props.handleClose();
  };

  const handleYes = () => {
    setDialogDetails({
      ...dialogDetails,
      open: false,
    });
    props.handleSwitch(props.newRowId)

  };

  return (
    <div>
      <Dialog
        open={dialogDetails.open}
        onClose={handleClose}
        maxWidth="md"
        fullWidth
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <StyledHeaderTitle id="alert-dialog-title">
          {props.newColId === "is_created_schedule" ? "Push To LiteCard" : "Change Active Status"}
        </StyledHeaderTitle>
        <DialogContent>
          <Box pt={3}>
            Are you sure you want to {props.newColId === "is_created_schedule" ? "push to liteCard" : "change the active status"}?
          </Box>
        </DialogContent>
        <DialogActions styled={{ margin: "5px 10px" }}>
          <Button onClick={handleClose} color="error" variant="contained" autoFocus>
            No
          </Button>
          <Button
            onClick={handleYes}
            color="primary"
            variant="contained"
            autoFocus
          >
            Yes
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

function TablePaginationActions(props) {
  const { count, page, rowsPerPage, onPageChange } = props;
  const theme = useTheme();

  const handleBackButtonClick = (event) => {
    onPageChange(event, page - 1);
  };

  const handleNextButtonClick = (event) => {
    onPageChange(event, page + 1);
  };

  return (
    <div style={{ flexShrink: "0" }}>
      <IconButton
        onClick={handleBackButtonClick}
        disabled={page === 1}
        aria-label="previous page"
      >
        {theme.direction === "rtl" ? (
          <KeyboardArrowRight />
        ) : (
          <KeyboardArrowLeft />
        )}
      </IconButton>
      <IconButton
        onClick={handleNextButtonClick}
        disabled={page >= Math.ceil(count / rowsPerPage)}
        aria-label="next page"
      >
        {theme.direction === "rtl" ? (
          <KeyboardArrowLeft />
        ) : (
          <KeyboardArrowRight />
        )}
      </IconButton>
    </div>
  );
}

TablePaginationActions.propTypes = {
  count: PropTypes.number.isRequired,
  onPageChange: PropTypes.func.isRequired,
  page: PropTypes.number.isRequired,
  rowsPerPage: PropTypes.number.isRequired,
};

const login = localStorage.getItem("login");
const loginData = JSON.parse(login);

export default function TableComponent(props) {

  const [rowStatus, setRowStatus] = React.useState({});
  const [openSwitchDialog, setOpenSwitchDialog] = React.useState(false);
  const [newRowId, setNewRowId] = React.useState("");
  const [newColId, setNewColId] = React.useState("");

  React.useEffect(() => {
    const initialState = {};
    props.rows.forEach((row) => {
      initialState[row.id] = (row.status || row.is_active || row.is_created_schedule) === 1;
    });
    setRowStatus(initialState);
  }, [props.rows]);

  const handleChange = (rowId, colId) => {
    if (rowStatus[rowId.id] && colId === "is_created_schedule") {
      return;
    } else {
      setOpenSwitchDialog(true);
      setNewRowId(rowId);
      setNewColId(colId);
    }
  };

  const handleClose = () => {
    setOpenSwitchDialog(false);
    setNewRowId("");
  };

  const handleSwitch = (rowId) => {
    props.setRowLoading((prev) => ({ ...prev, [rowId.id]: true }));

    const currentStatus = rowStatus[rowId.id] || false;
    const newStatus = !currentStatus;

    setRowStatus((prevStatus) => ({
      ...prevStatus,
      [rowId.id]: newStatus,
    }));

    handleClose();

    props.updateRowStatus(rowId)?.finally(() => {
      props.setRowLoading((prev) => ({ ...prev, [rowId.id]: false }));
    });
  };


  return (
    <Paper sx={{ width: "100%", overflow: "hidden" }}>
      <TableContainer component={Paper}>
        <Table sx={{ minWidth: 700 }} aria-label="customized table">
          <TableHead>
            <TableRow>
              {props.columns.map((col, index) =>
                props.sort ? (
                  ((index === (props.columns.length)) || index == 0 || col.id === "view" || col.id !== "email" || index === (props.columns.length - 1)) ?
                    <StyledTableCell>

                      {col.name}
                      {/* {props.currentColumn === col.id ? <span style={{ fontWeight:"700" }}>{col.name}</span> : col.name}
                   {props.currentColumn === col.id ? props.direction ? <KeyboardArrowUp fontSize="small" /> : <KeyboardArrowDown fontSize="small" /> : <UnfoldMore fontSize="small" />}   */}
                    </StyledTableCell>

                    :
                    <StyledTableCell align="" onClick={() => props.handleSort(col.id)}>
                      {props.currentColumn === col.id ? <span style={{ fontWeight: "700" }}>{col.name}</span> : col.name}
                      {props.currentColumn === col.id ? props.direction ? <KeyboardArrowUp fontSize="small" /> : <KeyboardArrowDown fontSize="small" /> : <UnfoldMore fontSize="small" />}
                    </StyledTableCell>

                ) : index === 0 ? (
                  <StyledTableCell>{col.name}</StyledTableCell>
                ) : (
                  <StyledTableCell align="">{col.name}</StyledTableCell>
                )
              )}
            </TableRow>
          </TableHead>
          {props.loading ? (
            <TableBody
              sx={{ position: "relative", height: "465px", overflow: "hidden" }}
            >
              <Box p={3} sx={{ position: "absolute", width: "100%" }}>
                <Skeleton height={60} />
                <Skeleton height={60} />
                <Skeleton height={60} />
                <Skeleton height={60} />
                <Skeleton height={60} />
                <Skeleton height={60} />
                <Skeleton height={60} />
              </Box>
            </TableBody>
          ) : (
            <TableBody>
              {props.rows.length ?
                props.rows
                  // .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((row, index1) => {


                    return (
                      <StyledTableRow
                        key={Math.random()}
                        onClick={() => (props.handleView && props.view !== true) ? props.handleView(row) : ""}
                        sx={{
                          cursor: (props.handleView && props.view !== true) ? "pointer" : "default", '&:last-child td, &:last-child th': { border: 0 },
                          background: row.isRedeemedTransection === 1 ? "#d1ffbd !important" : ""
                        }}
                      >
                        {props.columns.map((col, index) =>

                          (col.id === "sn") ? (
                            <StyledTableCell align="" style={{ zIndex: "1" }}  >

                              {Number(props.fromTable) + index1}

                            </StyledTableCell>
                          )
                            :
                            col.id === "image" ? (
                              index === 0 ? (
                                <StyledTableCell component="th" scope="row">
                                  <img
                                    src={row.image}
                                    style={{
                                      width: "100px",
                                      height: "100px",
                                      objectFit: "cover",
                                    }}
                                    alt={row.firstname}
                                  />
                                </StyledTableCell>
                              ) : (
                                <StyledTableCell align="">
                                  <img
                                    src={row.image}
                                    style={{
                                      width: "100px",
                                      height: "100px",
                                      objectFit: "cover",
                                    }}
                                    alt={row.firstname}
                                  />
                                </StyledTableCell>
                              )
                            ) : index === 0 ? (
                              <StyledTableCell component="th" scope="row">
                                {row[col.id]}
                              </StyledTableCell>
                            ) : col.id === "description" ||
                              col.id === "shortDescription" ||
                              col.id === "text" ? (
                              <StyledTableCell align="left" sx={{ width: "500px" }}>
                                {(
                                  row[col.id].substring(0, 100) +
                                  (row[col.id].length > 100 ? "..." : " ")
                                ).replaceAll(/<[^>]+>/g, "")}
                              </StyledTableCell>
                            ) : (
                              <StyledTableCell align="">
                                {
                                  col.id === "actions" ?

                                    <>
                                      <OptionMenu
                                        name={props.name}
                                        row={row}
                                        options={props.options}
                                        currentChange={props.currentChange}
                                      />
                                    </>
                                    :
                                    //     loginData.UserLevel === 1 ?
                                    //       <>
                                    //         <OptionMenu
                                    //           row={row}
                                    //           options={props.options}
                                    //           currentChange={props.currentChange}
                                    //         />
                                    //       </> : <></>

                                    // : 
                                    col.id === "project_status" ? (
                                      row[col.id] === 1 ? (
                                        <Check color="primary" />
                                      ) : (
                                        <Clear color="primary" />
                                      )
                                    ) : (col.id === "status" || col.id === "statusRule" || col.id === "is_created_schedule") && !row.point_transections ? (
                                      <>
                                        {/* <Switch
                                          checked={rowStatus[row.id] || false}
                                          onChange={() => handleChange(row, col.id)}
                                          inputProps={{ 'aria-label': 'controlled' }}
                                          disabled={props.rowLoading[row.id]}
                                        /> */}
                                        {props.rowLoading[row.id] ?
                                          <CircularProgress size={20} />
                                          :
                                          <Switch
                                            checked={rowStatus[row.id] || false}
                                            onChange={() => handleChange(row, col.id)}
                                            inputProps={{ 'aria-label': 'controlled' }}
                                            disabled={props.rowLoading[row.id]}
                                          />
                                        }
                                      </>
                                    ) : col.id === "consumer_active" ? (
                                      row[col.id] === 1 ? (
                                        <Check color="primary" />
                                      ) : (
                                        <Clear color="primary" />
                                      )
                                    ) : col.id === "menuList" ? (
                                      <Tooltip title={"View Permission Menu List"}>
                                        <IconButton><Menu onClick={() => props.handleViewMenuList(row)} color="primary" /></IconButton>
                                      </Tooltip>
                                    ) : col.id === "view" ? (
                                      <Tooltip title={"View Details"}>
                                        <IconButton><Preview onClick={() => props.handleNewView(row)} color="primary" /></IconButton>
                                      </Tooltip>
                                    ) : col.id === "projectList" ? (
                                      <Tooltip title={"View Project List"}>
                                        <IconButton><Menu onClick={() => props.handleViewProjectList(row)} color="primary" /></IconButton>
                                      </Tooltip>
                                    ) : col.id === "moveToWorkflow" ? (
                                      <IconButton onClick={() => props.handleView(row)}><Tooltip title="View In Workflow"><DoubleArrow /></Tooltip></IconButton>
                                    ) : col.id === "actionModule" ? (
                                      <Button variant="contained" onClick={() => props.handleView(row)}>View Details</Button>
                                    ) : col.id === "actionModuleDetail" ? (
                                      <Button variant="contained" onClick={() => props.handleView(row)}>View Details</Button>
                                    ) : col.id === "statusInfo" ? (
                                      row[col.id]?.status === "Error" ? <span style={{ color: "red", fontWeight: "600" }}> Error {row[col.id]?.data?.errorCount !== 0 && `(${row[col.id]?.data?.errorCount})`}</span>

                                        : row[col.id]?.status === "Pending" ? <span style={{ color: "#F7B500", fontWeight: "600" }}>Pending {row[col.id]?.data?.pendingCount !== 0 && `(${row[col.id]?.data?.pendingCount})`}</span>

                                          : row[col.id]?.status === "Complete" ? <span style={{ color: "green", fontWeight: "600" }}>Completed </span>
                                            : row[col.id]?.status === "success" ? <span style={{ color: "green", fontWeight: "600" }}>Success </span>
                                              : "-"
                                    ) :
                                      col.id === "is_system_admin" ? (
                                        row[col.id] === 1 ? (
                                          <Check color="primary" />
                                        ) : (
                                          <Clear color="primary" />
                                        )
                                      ) : col.id === "is_active" ? (
                                        row[col.id] === 1 ? (
                                          <Check color="primary" />
                                        ) : (
                                          <Clear color="primary" />
                                        )
                                      ) : col.id === "is_deleted" ? (
                                        row[col.id] === 0 ? (
                                          <Check color="primary" />
                                        ) : (
                                          <Clear color="primary" />
                                        )
                                      ) : col.id === "lastUpdatedModule" ? (
                                        row.lastUpdated
                                      ) : col.id === "ls_created_at" ? (
                                        moment(row.ls_created_at).format(
                                          "ddd, DD MMM YYYY, h:mm A"
                                        )
                                      ) :
                                        (col.id === "lastUpdated" || col.id === "schedule_date" || col.id === "start_date" || col.id === "end_date") ? (
                                          (row[col.id] !== null && row[col.id] !== "") ?
                                            moment(row[col.id]).format(
                                              "ddd, DD MMM YYYY, h:mm:ss a"
                                            )
                                            : "-"
                                        ) : col.id === "added_date" ? (
                                          moment(row[col.id]).format(
                                            "ddd, DD MMM YYYY, h:mm:ss a"
                                          )
                                        ) : col.id === "updated_date" ? (
                                          moment(row[col.id]).format(
                                            "ddd, DD MMM YYYY, h:mm:ss a"
                                          )
                                        ) :
                                          col.id === "site_id" ? (
                                            row.site?.name
                                          ) :
                                            col.id === "staff_member_id" ? (
                                              [
                                                row.staff?.first_name,
                                                row.staff?.last_name
                                              ].filter(Boolean).join(' ')

                                            ) :
                                              col.id === "UserLevel" ? (
                                                row[col.id] === 1 ? "Admin" : "Staff"
                                              ) :
                                                col.id === "userModules" ? (
                                                  Array.isArray(row[col.id]) ? row[col.id].join(', ') : row[col.id]
                                                ) :
                                                  col.id === "modules" ? (
                                                    Array.isArray(row[col.id]) ? row[col.id].map(mod => mod.name).join(', ') : row[col.id]
                                                  )
                                                    : col.id === "total" ? ("$" + parseFloat(row.total).toFixed(2)) :
                                                      col.id === "paid" ? ("$" + parseFloat(row.paid).toFixed(2)) :
                                                        (
                                                          row[col.id] || "-"
                                                        )}
                              </StyledTableCell>
                            )
                        )}
                      </StyledTableRow>
                    )
                  })
                :
                <TableRow sx={{ position: "relative", height: "50px" }}>
                  <TableCell sx={{ position: "absolute", right: "50%", borderBottom: "none" }}>
                    No Records Found
                  </TableCell>
                </TableRow>
              }
            </TableBody>
          )}

          {props.footer !== false && (
            <TableFooter>
              <TableRow>
                <TablePagination
                  rowsPerPageOptions={[20, 50, 70, 100]}
                  rowsPerPage={props.rowsPerPage}
                  page={props.page}
                  count={props.total && props.total}
                  SelectProps={{
                    native: true,
                  }}
                  labelDisplayedRows={() =>
                    `${props.fromTable !== null ? props.fromTable : "0"} - ${props.toTable !== null ? props.toTable : "0"
                    } to ${props.total}`
                  }
                  onPageChange={props.handleChangePage}
                  onRowsPerPageChange={props.handleChangeRowsPerPage}
                  ActionsComponent={TablePaginationActions}
                />
              </TableRow>
            </TableFooter>
          )}
        </Table>
      </TableContainer>
      {openSwitchDialog &&
        <SwitchDialog newRowId={newRowId} newColId={newColId} handleSwitch={handleSwitch} handleClose={handleClose} />
      }
      {/* {props.footer !== false && (
        <TablePagination
          rowsPerPageOptions={[10, 25, 100]}
          component="div"
          count={props.rows.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      )} */}
    </Paper>
  );
}
