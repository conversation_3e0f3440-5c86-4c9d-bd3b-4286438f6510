import React, { useEffect, useState } from "react";
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';

import Exporting from "highcharts/modules/exporting";
import ExportData from "highcharts/modules/export-data";


if (typeof Highcharts === "object") {
  if (!Highcharts.Chart.prototype.exportChart) Exporting(Highcharts);
  if (!Highcharts.Chart.prototype.downloadCSV) ExportData(Highcharts);
}


const GraphGridLightspeed = ({ loading, chartData }) => {

  const options = chartData
    ? {
      chart: {
        type: 'line',
      },
      title: {
        text: chartData.title,
      },
      xAxis: {
        categories: chartData.categories,
      },
      yAxis: {
        title: {
          text: 'Count (Numbers)',
        },
      },
      series: chartData.series,
      tooltip: {
        shared: false,
        valueSuffix: '',
      },
      credits: {
        enabled: false,
      },
      colors: chartData.colors,
      exporting: {
        enabled: true,
        buttons: {
          contextButton: {
            menuItems: chartData.exportButtons,
            symbol: chartData.exportIcon,
            symbolSize: chartData.exportIconSize,
            symbolX: chartData.exportIconAxisX,
            symbolY: chartData.exportIconAxisY,
          }
        },
      },
    }
    : {};
  return (
    <div className="grid-block">
      {/* <div className="grid-block-header">Lightspeed</div> */}
      {loading ? (
        <div style={{
          position: "relative",
          width: '100%',
          height: '180px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'rgba(255, 255, 255, 0.8)',
          zIndex: 10,
        }}>
          <span><strong>Loading Chart...</strong></span>
        </div>
      ) : (
        <HighchartsReact highcharts={Highcharts} options={options} />
      )}
      <style jsx>{`
            .grid-block {
              border: 0.5px solid #gray;
              box-shadow: 0px 5px 20px 0px rgba(0,0,0,0.07);
              padding: 0px;
              background-color: #fff;
              border-radius: 5px;
            }
    
            .grid-block-header {
              font-size: 18px;
              font-weight: bold;
              padding: 10px;
              margin-top: 0.3px;
              background-color: #281E50;
              color: #fff;
              border-radius: 3px;
              height: 100%;
              min-height: 70px;
              display: flex;
              align-items: center;
            }
    
            .grid-section {
              margin-top: 10px;
            }
    
            .section-header {
              font-size: 16px;
              font-weight: bold;
              padding: 10px;
              margin-top:-10px;
              background-color: #f5f5f5;
              color: black;
              border-radius: 3px;
            }
    
            .grid-block-content {
              display: grid;
              width: 100%;
              grid-template-columns: 1fr auto;
              gap: 1px solid #ccc;
              font-family: "Trebuchet MS", sans-serif;
              font-size: 14px;
              font-weight: bold;
            }
    
            .grid-item.title {
              border-right: 1px solid #f1f1f1;
              border-bottom: 1px solid #f1f1f1;
              background-color: #ffffff;
              padding: 16px;
            }
    
            .grid-item.content {
              border-bottom: 1px solid #f1f1f1;
              background-color: #ffffff;
              padding: 16px;
              min-width: 120px;
              text-align:right;
            }
          `}</style>
    </div>
  );
};

export default GraphGridLightspeed;
