<!-- Experience section -->
<section class="py-16 px-4 sm:px-6 lg:px-8 bg-white">
    <h2 class="text-3xl sm:text-4xl font-bold text-center mb-12 text-gray-800">THE BAJAJ EXPERIENCES</h2>

    <div class="relative max-w-6xl mx-auto flex items-center justify-center">
        <button id="leftArrow" class="nav-arrow absolute left-0 z-20 focus:outline-none -ml-6 sm:-ml-10 lg:-ml-12">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
        </button>

        <div class="experience-carousel-wrapper max-w-4xl mx-auto">
            <div id="experienceCarouselContainer" class="experience-carousel-container">
                @if(isset($experiences) && count($experiences) > 0)
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach($experiences as $experience)
                            <div class="experience-card bg-white rounded-lg shadow-lg overflow-hidden transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
                                <div class="relative h-48 overflow-hidden">
                                    <img src="{{ $experience->image ?? $experience['image'] }}" 
                                         alt="{{ $experience->title ?? $experience['title'] }}" 
                                         class="w-full h-full object-cover transition-transform duration-300 hover:scale-110" />
                                    <div class="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>
                                </div>
                                <div class="p-6">
                                    <h3 class="text-xl font-bold text-gray-900 mb-2">
                                        {{ $experience->title ?? $experience['title'] }}
                                    </h3>
                                    <p class="text-gray-600 text-sm mb-4">
                                        {{ $experience->description ?? $experience['description'] }}
                                    </p>
                                    <a href="{{ $experience->link ?? $experience['link'] ?? '#' }}" 
                                       class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium transition-colors duration-200">
                                        Learn More
                                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <!-- Default experiences if no data -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div class="experience-card bg-white rounded-lg shadow-lg overflow-hidden transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
                            <div class="relative h-48 overflow-hidden">
                                <img src="{{ asset('assets/experience/1.png') }}" 
                                     alt="Squad Diaries" 
                                     class="w-full h-full object-cover transition-transform duration-300 hover:scale-110" />
                                <div class="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>
                            </div>
                            <div class="p-6">
                                <h3 class="text-xl font-bold text-gray-900 mb-2">Squad Diaries</h3>
                                <p class="text-gray-600 text-sm mb-4">Adventures with your riding squad.</p>
                                <a href="#" class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium transition-colors duration-200">
                                    Learn More
                                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>

                        <div class="experience-card bg-white rounded-lg shadow-lg overflow-hidden transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
                            <div class="relative h-48 overflow-hidden">
                                <img src="{{ asset('assets/experience/2.png') }}" 
                                     alt="Grand Exchange Mela" 
                                     class="w-full h-full object-cover transition-transform duration-300 hover:scale-110" />
                                <div class="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>
                            </div>
                            <div class="p-6">
                                <h3 class="text-xl font-bold text-gray-900 mb-2">Grand Exchange Mela</h3>
                                <p class="text-gray-600 text-sm mb-4">Exciting offers on new Bajaj bikes.</p>
                                <a href="#" class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium transition-colors duration-200">
                                    Learn More
                                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>

                        <div class="experience-card bg-white rounded-lg shadow-lg overflow-hidden transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
                            <div class="relative h-48 overflow-hidden">
                                <img src="{{ asset('assets/experience/3.png') }}" 
                                     alt="Auto Show 2025" 
                                     class="w-full h-full object-cover transition-transform duration-300 hover:scale-110" />
                                <div class="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>
                            </div>
                            <div class="p-6">
                                <h3 class="text-xl font-bold text-gray-900 mb-2">Auto Show 2025</h3>
                                <p class="text-gray-600 text-sm mb-4">Experience the latest innovations.</p>
                                <a href="#" class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium transition-colors duration-200">
                                    Learn More
                                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <button id="rightArrow" class="nav-arrow absolute right-0 z-20 focus:outline-none -mr-6 sm:-mr-10 lg:-mr-12">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
        </button>
    </div>
</section>

@push('scripts')
<script>
    // Experience carousel functionality
    document.addEventListener('DOMContentLoaded', function() {
        const leftArrow = document.getElementById('leftArrow');
        const rightArrow = document.getElementById('rightArrow');
        const container = document.getElementById('experienceCarouselContainer');
        
        if (leftArrow && rightArrow && container) {
            let currentIndex = 0;
            const cards = container.querySelectorAll('.experience-card');
            const totalCards = cards.length;
            const cardsPerView = window.innerWidth >= 1024 ? 3 : window.innerWidth >= 768 ? 2 : 1;
            const maxIndex = Math.max(0, totalCards - cardsPerView);
            
            function updateCarousel() {
                const translateX = -(currentIndex * (100 / cardsPerView));
                container.style.transform = `translateX(${translateX}%)`;
            }
            
            leftArrow.addEventListener('click', function() {
                currentIndex = Math.max(0, currentIndex - 1);
                updateCarousel();
            });
            
            rightArrow.addEventListener('click', function() {
                currentIndex = Math.min(maxIndex, currentIndex + 1);
                updateCarousel();
            });
            
            // Auto-advance carousel
            setInterval(function() {
                currentIndex = currentIndex >= maxIndex ? 0 : currentIndex + 1;
                updateCarousel();
            }, 5000);
        }
    });
</script>
@endpush
