{"name": "webresume-manager", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tooltip": "^1.2.0", "@reduxjs/toolkit": "^2.7.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.488.0", "mongoose": "^8.13.2", "next": "15.3.1", "next-auth": "^5.0.0-beta.25", "next-cloudinary": "^6.16.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-redux": "^9.2.0", "react-toastify": "^11.0.5", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "22.14.1", "@types/react": "19.1.2", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4"}}