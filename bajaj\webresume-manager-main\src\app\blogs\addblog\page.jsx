"use client";

import BlogForm from "@/components/BlogForm";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

const page = () => {
  const router = useRouter();

  //redidrect to login if not authenticated
  const { data: session, status } = useSession();

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/");
    }
  }, [status, router]);

  return (
    <div className="px-4 md:px-8 xl:px-16">
      <BlogForm />
    </div>
  );
};

export default page;
