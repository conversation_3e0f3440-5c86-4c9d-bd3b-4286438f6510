<div class="relative dropdown">
    <button class="text-sm flex items-center space-x-1 text-gray-700 hover:text-blue-600 transition-colors duration-200" onclick="toggleDropdown('motorcycles')">
        <span>MOTORCYCLES</span>
        <svg class="w-4 h-4 transition-transform duration-200" id="motorcycles-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
    </button>

    <!-- Dropdown Content -->
    <div id="motorcycles-dropdown" class="dropdown-content absolute top-full left-0 mt-12 bg-white border border-gray-200 z-50 overflow-hidden" style="width: 1000px; height: 600px;">
        <div class="flex h-full">
            <!-- Left Sidebar - Brands -->
            <div class="w-64 bg-gray-50 p-6 rounded-l-lg flex-shrink-0">
                <h3 class="text-gray-800 font-semibold mb-4">BRANDS</h3>
                <ul class="space-y-2" id="brand-list">
                    @if(isset($bikeBrands))
                        @foreach($bikeBrands as $brandName => $bikes)
                            <li>
                                <button class="brand-item w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded transition-colors" data-brand="{{ $brandName }}">
                                    {{ $brandName }}
                                </button>
                            </li>
                        @endforeach
                    @else
                        <!-- Default brands if no data passed -->
                        <li><button class="brand-item w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded transition-colors" data-brand="PULSAR">PULSAR</button></li>
                        <li><button class="brand-item w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded transition-colors" data-brand="DOMINAR">DOMINAR</button></li>
                        <li><button class="brand-item w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded transition-colors" data-brand="AVENGERS">AVENGERS</button></li>
                        <li><button class="brand-item w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded transition-colors" data-brand="DISCOVER">DISCOVER</button></li>
                        <li><button class="brand-item w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded transition-colors" data-brand="PLATINA">PLATINA</button></li>
                    @endif
                </ul>
            </div>

            <!-- Right Content - Motorcycles -->
            <div class="flex-1 flex flex-col h-full">
                <!-- Category Filter -->
                <div class="flex space-x-4 p-6 pb-4 flex-shrink-0 border-b border-gray-100">
                    <button class="category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 active" onclick="filterCategory('All')">All</button>
                    <button class="category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-200" onclick="filterCategory('Classic')">Classic</button>
                    <button class="category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-200" onclick="filterCategory('NS')">NS</button>
                    <button class="category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-200" onclick="filterCategory('N')">N</button>
                    <button class="category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-200" onclick="filterCategory('Adventure')">Adventure</button>
                    <button class="category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-200" onclick="filterCategory('Cruiser')">Cruiser</button>
                    <button class="category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-200" onclick="filterCategory('Commuter')">Commuter</button>
                    <button class="category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-200" onclick="filterCategory('Economy')">Economy</button>
                </div>

                <!-- Motorcycle Grid with Scrolling -->
                <div id="motorcycle-grid" class="flex-1 overflow-y-auto px-6 py-4">
                    <!-- Motorcycles will be populated by JavaScript or server-side -->
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                        <!-- Placeholder content - will be replaced by dynamic content -->
                        <div class="text-center text-gray-500 col-span-full py-8">
                            Select a brand to view motorcycles
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
