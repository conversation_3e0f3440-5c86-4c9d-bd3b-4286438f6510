"use client";

import { deleteExperience, getExperienceById } from "@/actions/experience";
import { Button } from "@/components/ui/button";
import { useSession } from "next-auth/react";
import { CldUploadWidget } from "next-cloudinary";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";

const ExperienceForm = ({ experienceId }) => {
  const router = useRouter();
  const [errors, setErrors] = useState({});
  const [success, setSuccess] = useState(false);
  const [working, setWorking] = useState(true);
  const { data: session, status } = useSession();

  //toast message to show success message
  useEffect(() => {
    if (success) {
      experienceId
        ? toast.success("Experience updated successfully!")
        : toast.success("Experience added successfully!");
      router.push("/experiences");
    }
  }, [success, router]);

  //handling form data with image
  const [img, setImg] = useState("");
  const [formData, setFormData] = useState({
    role: "",
    company: "",
    joiningDate: "",
    resignDate: "working",
    desc: "",
  });

  //get details of existing experience in case of edit
  useEffect(() => {
    const getExperience = async () => {
      if (experienceId) {
        const experience = await getExperienceById(experienceId);
        setFormData({
          role: experience.role,
          company: experience.company,
          desc: experience.desc,
          joiningDate: experience.joiningDate,
          resignDate: experience.resignDate,
        });
        setImg(experience.image);
      }
    };

    getExperience();
  }, [experienceId]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    const userId = session.user.userId;

    const res = await fetch(`/api/experience/${userId}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        role: formData.role,
        company: formData.company,
        joiningDate: formData.joiningDate,
        resignDate: formData.resignDate,
        desc: formData.desc,
        image: img || "",
      }),
    });

    const data = await res.json();
    if (res.ok) {
      //console.log("experience created:", data);
      setFormData({
        role: "",
        company: "",
        joiningDate: "",
        resignDate: "working",
        desc: "",
      });
      setImg("");
      setSuccess(true);
      setErrors({});

      //delete old experience after updating
      if (experienceId) {
        await deleteExperience(experienceId);
      }
    } else {
      //console.log("errror", data);
      setErrors(data.error || { general: [data.error] });
      setSuccess(false);
    }
  };

  if (status === "loading") return <p>Loading...</p>;

  return (
    <div className="px-4 md:px-8 xl:px-16">
      <div className="flex items-center gap-16 mb-12">
        <h1 className="text-3xl font-semibold">
          {experienceId ? "Edit Experience" : "Add New Experience"}
        </h1>
      </div>

      <form onSubmit={handleSubmit} className="flex flex-col gap-6">
        <div className="flex flex-col">
          <label htmlFor="role">Role</label>
          <input
            type="text"
            name="role"
            placeholder="Enter role"
            value={formData.role}
            onChange={(e) =>
              setFormData({
                ...formData,
                role: e.target.value,
              })
            }
            className="border border-gray-400 px-2 py-1 outline-none"
            required
          />
          {errors.role && <p className="text-red-500">{errors.role[0]}</p>}
        </div>

        <div className="flex flex-col">
          <label htmlFor="company">Company</label>
          <input
            type="text"
            name="company"
            placeholder="Enter company name"
            value={formData.company}
            onChange={(e) =>
              setFormData({
                ...formData,
                company: e.target.value,
              })
            }
            className="border border-gray-400 px-2 py-1 outline-none"
            required
          />
          {errors.company && (
            <p className="text-red-500">{errors.company[0]}</p>
          )}
        </div>

        <CldUploadWidget
          uploadPreset="portfolio-backend"
          onSuccess={(result) => setImg(result?.info.secure_url)}
        >
          {({ open }) => {
            return (
              <div className="flex flex-col">
                <label htmlFor="image">Image</label>
                <Image
                  src={img || "/no-image.png"}
                  width={140}
                  height={100}
                  alt="image"
                  className="w-20 h-auto rounded-lg"
                />
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    open();
                  }}
                  name="imageUrl"
                  className="border border-gray-400 px-2 py-1 outline-none cursor-pointer"
                >
                  Choose Image
                </button>
              </div>
            );
          }}
        </CldUploadWidget>

        <div className="flex flex-col">
          <label htmlFor="joiningDate">Joining Date</label>
          <input
            type="date"
            name="joiningDate"
            value={formData.joiningDate}
            onChange={(e) =>
              setFormData({
                ...formData,
                joiningDate: e.target.value,
              })
            }
            className="border border-gray-400 px-2 py-1 outline-none"
          />
          {errors.joiningDate && (
            <p className="text-red-500">{errors.joiningDate[0]}</p>
          )}
        </div>

        <div>
          <input
            type="checkbox"
            checked={working}
            onChange={(e) => setWorking(e.target.checked)}
          />
          <label> I am currently working here</label>
        </div>

        {!working && (
          <div className="flex flex-col">
            <label htmlFor="resignDate">Resign Date</label>
            <input
              type="date"
              name="resignDate"
              value={formData.resignDate}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  resignDate: e.target.value,
                })
              }
              className="border border-gray-400 px-2 py-1 outline-none"
            />
            {errors.resignDate && (
              <p className="text-red-500">{errors.resignDate[0]}</p>
            )}
          </div>
        )}

        <div className="flex flex-col">
          <label htmlFor="desc">Work Description</label>
          <textarea
            name="desc"
            cols="40"
            rows="8"
            value={formData.desc}
            onChange={(e) =>
              setFormData({
                ...formData,
                desc: e.target.value,
              })
            }
            placeholder="Describe your work highlights here"
            className="border border-gray-400 px-2 py-1 outline-none"
            required
          ></textarea>
          {errors.desc && <p className="text-red-500">{errors.desc[0]}</p>}
        </div>

        <Button
          type="submit"
          className="mb-10 bg-gray-800 text-white cursor-pointer hover:text-stone-200"
        >
          {experienceId ? "Update Experience" : "Add Experience"}
        </Button>
      </form>
    </div>
  );
};

export default ExperienceForm;
