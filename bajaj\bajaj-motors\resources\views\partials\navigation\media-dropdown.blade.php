<div class="relative dropdown">
    <button class="flex items-center space-x-1 text-gray-700 hover:text-blue-600 transition-colors duration-200" onclick="toggleDropdown('media')">
        <span>MEDIA CENTER</span>
        <svg class="w-4 h-4 transition-transform duration-200" id="media-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
    </button>

    <!-- Media Dropdown Content -->
    <div id="media-dropdown" class="media-dropdown-content absolute top-full right-0 mt-12 bg-white border border-gray-200 z-50 hidden" style="width: 220px">
        <div class="py-2">
            <a href="{{ route('about') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">
                ABOUT US
            </a>
            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">
                ANNOUNCEMENTS
            </a>
            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">
                EVENTS
            </a>
            <a href="{{ route('blogs.index') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">
                BLOGS
            </a>
            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">
                DOWNLOAD CENTER
            </a>
            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">
                CONTACT US
            </a>
            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">
                FAQS
            </a>
        </div>
    </div>
</div>
