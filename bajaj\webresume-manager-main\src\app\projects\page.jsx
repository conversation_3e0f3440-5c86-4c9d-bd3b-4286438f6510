"use client";
import { deleteProject } from "@/actions/project";
import { Button } from "@/components/ui/button";
import { FilePenLine, Trash2 } from "lucide-react";
import { useSession } from "next-auth/react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { toast } from "react-toastify";

const page = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [projects, setProjects] = useState([]);

  //check authentication
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/");
    }
  }, [status, router]);

  //fetch projects
  useEffect(() => {
    const fetchProjects = async () => {
      if (!session?.user?.userId) return;

      try {
        const res = await fetch(`/api/project/${session.user.userId}`);
        const data = await res.json();
        setProjects(data);
      } catch (err) {
        console.error("Error fetching Projects:", err);
      }
    };

    fetchProjects();
  }, [session, status]);

  //handle delete project
  const handleDelete = async (projectId) => {
    try {
      await deleteProject(projectId);
      setProjects((prevProjects) =>
        prevProjects.filter((p) => p._id !== projectId)
      );
      toast.success("Project deleted successfully!");
    } catch (error) {
      console.log("Error in handle delete", error);
    }
  };

  //handle edit project
  const handleEdit = (projectId) => {
    router.push(`/projects/editproject/?id=${projectId}`);
  };

  if (status === "loading") {
    return <p>Loading...</p>;
  }

  return (
    <div className="px-4 md:px-8 xl:px-16">
      <div className="flex items-center gap-16 mb-12">
        <h1 className="text-3xl font-semibold">Projects Management</h1>
        <Link href={"/projects/addproject"}>
          <button className="bg-gray-900 rounded-xl text-white px-4 py-2 hover:bg-gray-800 cursor-pointer">
            Add Project
          </button>
        </Link>
      </div>

      <div className="flex gap-6 flex-wrap">
        {projects.length ? (
          projects.map((project) => (
            <div
              key={project._id}
              className="bg-gray-100 border shadow-lg w-72 rounded-xl"
            >
              <Image
                src={project.image || "/no-image.png"}
                width={240}
                height={240}
                alt="project image"
                className="rounded-t-xl h-60 w-full object-cover"
              />
              <div>
                <h2 className="text-xl font-semibold py-3 px-2">
                  {project.title}
                </h2>
                <p className="text-sm px-2 text-gray-600">{project.desc}</p>
                <div className="flex justify-around py-4">
                  {project.githubLink && (
                    <Link
                      href={project.githubLink}
                      className="px-4 py-2 text-sm bg-gray-300 rounded-[6px] text-center hover:bg-gray-400 duration-200 ease-in-out"
                    >
                      View Code
                    </Link>
                  )}
                  {project.liveLink && (
                    <Link
                      href={project.liveLink}
                      className="px-4 py-2 text-sm bg-gray-300 rounded-[6px] text-center hover:bg-gray-400 duration-200 ease-in-out"
                    >
                      View Demo
                    </Link>
                  )}
                </div>

                <div className="flex items-center justify-end gap-2 px-2  pb-4">
                  <Button
                    onClick={() => handleEdit(project._id)}
                    className="bg-gray-200 hover:bg-gray-300 cursor-pointer"
                  >
                    <FilePenLine />
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={() => handleDelete(project._id)}
                    className="bg-gray-200 hover:bg-gray-300 cursor-pointer"
                  >
                    <Trash2 className="text-red-500" />
                  </Button>
                </div>
              </div>
            </div>
          ))
        ) : (
          <p>No Projects Found....</p>
        )}
      </div>
    </div>
  );
};

export default page;
